<?php
session_start();
$file = $_SERVER['PHP_SELF'];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    include_once 'connect.php';
    $Hometop = isset($_POST['Hometop']) ? $_POST['Hometop'] : 0;
    $Announcement = $_POST['Announcement'];
    $Displaytime  = $_POST['Displaytime'];
    $Searchtext = $_POST['Searchtext'];
    $Carouselswitch = isset($_POST['Carouselswitch']) ? $_POST['Carouselswitch'] : 0;
    $Iconswitch = isset($_POST['Iconswitch']) ? $_POST['Iconswitch'] : 0;
    $Noticeswitch = isset($_POST['Noticeswitch']) ? $_POST['Noticeswitch'] : 0;
    $Notice = $_POST['Notice'];
    $circleOf = isset($_POST['circleOf']) ? $_POST['circleOf'] : 0;
    $homeMode = $_POST['homeMode'];
    $actStyle = $_POST['actStyle'];
    $topStyle = $_POST['topStyle'];
    $swiperStyle = $_POST['swiperStyle'];
    if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    $redisKeys = $connectRedis->keys('starapi_*');
        foreach ($redisKeys as $redisKey) {
            $connectRedis->del($redisKey);
        }
        // 写入数据库
        $query = "UPDATE Sy_pages SET Hometop=?, Announcement=?, Displaytime=?, Searchtext=?, Notice=?, Carouselswitch=?, Iconswitch=?, Noticeswitch=?, circleOf=?, homeMode=?, actStyle=?, topStyle=?, swiperStyle=?";
        $stmt = $connect->prepare($query);
        if ($stmt) {
            $stmt->bind_param("issssiiiissss", $Hometop, $Announcement, $Displaytime, $Searchtext, $Notice, $Carouselswitch, $Iconswitch, $Noticeswitch, $circleOf, $homeMode, $actStyle, $topStyle, $swiperStyle);

            if ($stmt->execute()) {
                echo "<script>alert('更改成功');location.href = 'homePage.php';</script>";
            } else {
                echo "<script>alert('更改失败');location.href = 'homePage.php';</script>";
            }
            $stmt->close();
        } else {
            echo "<script>alert('无法连接数据库');location.href = 'homePage.php';</script>";
        }
    } else {
        echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
    }
} else {
    
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
?>
