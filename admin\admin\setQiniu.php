<?php
session_start();
?>


<?php
include_once 'Nav.php';

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));
$response = curl_exec($curl);
$responseData = json_decode($response, true);  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $qiniuDomain = $responseData['data']['qiniuDomain'];    
    $qiniuAccessKey = $responseData['data']['qiniuAccessKey'];  
    $qiniuSecretKey = $responseData['data']['qiniuSecretKey'];  
    $qiniuBucketName = $responseData['data']['qiniuBucketName'];   
    

} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">七牛云上传配置</h4>
                <form class="needs-validation" action="setQiniuPost.php" method="post"
                      novalidate>
                    <div class="form-group mb-3">
                          <p>七牛云对象存储，和其它上传方式四选一配置即可，<a href="https://developer.qiniu.com/kodo" target="_blank">官方文档</a></p>
                    </div>
                    <div class="form-group mb-3">
                          <label for="qiniuDomain">七牛云访问域名
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              如https://cdn.domain.com
                          </span></label>
                          <input name="qiniuDomain" class="form-control" type="text" id="qiniuDomain" placeholder="请输入地域节点Endpoint" value="<?php echo $qiniuDomain;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="qiniuAccessKey">七牛云AccessKey</label>
                          <input name="qiniuAccessKey" class="form-control" type="text" id="qiniuAccessKey" placeholder="请输入七牛云AccessKey" value="<?php echo $qiniuAccessKey;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="qiniuSecretKey">七牛云SecretKey</label>
                          <input name="qiniuSecretKey" class="form-control" type="text" id="qiniuSecretKey" placeholder="请输入七牛云SecretKey" value="<?php echo $qiniuSecretKey;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="qiniuBucketName">存储桶名称</label>
                          <input name="qiniuBucketName" class="form-control" type="text" id="qiniuBucketName" placeholder="请输入存储桶名称" value="<?php echo $qiniuBucketName;  ?>">
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="setQiniuPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<?php
include_once 'Footer.php';
?>

</body>
</html>