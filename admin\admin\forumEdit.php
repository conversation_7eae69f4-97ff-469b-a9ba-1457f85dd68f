<?php
session_start();
?>



<?php
include_once 'Nav.php';
$id = $_GET['id'];
$withdrawals = "SELECT * FROM typecho_forum_section WHERE type = 'section' ORDER BY id DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
$article = "SELECT * FROM typecho_forum WHERE id='$id' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);
$section = $mod['section'];
$article2 = "SELECT * FROM typecho_forum_section WHERE id='$section' limit 1";
$resarticle2 = mysqli_query($connect, $article2);
$mod2 = mysqli_fetch_array($resarticle2);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">编辑帖子</h4>

                <form class="needs-validation" action="forumEditPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">帖子id</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="id" value="<?php echo $id ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['authorId'] ?>" required>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">所属圈子</label>
                            <select class="form-control" id="example-select" name="section">
                                <?php
                                while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {
                                    ?>
                                    <option value="<?php echo $withdrawal['id']; ?>" <?php if($withdrawal['id']==$section){echo "selected";} ?>><?php echo $withdrawal['name'] ?></option>
                                <?php
                                }
                                ?>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">帖子标题</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入标题"
                               name="articletitle" value="<?php echo $mod['title'] ?>" required>
                    </div>
                    <label for="validationCustom01">帖子内容</label>
                    <div id="editor—wrapper">
                        <div id="toolbar-container"></div>
                            <div id="editor-container"></div>
                             <textarea id="editorContent" name="articletext" style="display: none;"><?php echo $mod['text'] ?></textarea>  
                        </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="forumEditPost">修改帖子</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->
<link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
<script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>
<script>
const { createEditor, createToolbar } = window.wangEditor
const editorConfig = {
    placeholder: '请输入内容...',
    onChange(editor) {
      const html = editor.getHtml();  
    document.getElementById('editorContent').value = html;  
    },  
}
<?php 
$edithOf = true; 
include_once 'editor.php';
?>

<script>
    function check() {
        let title = document.getElementsByName('articletitle')[0].value.trim();
        let text = document.getElementsByName('articletext')[0].value.trim();
        if (title.length == 0) {
            alert("帖子标题不能为空");
            return false;
        } else if (text.length == 0) {
            alert("帖子内容不能为空");
            return false;
        }

    }

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>