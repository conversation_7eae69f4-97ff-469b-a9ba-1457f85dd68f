<?php
session_start();
?>

<?php
include_once 'Nav.php';
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">新增帖子</h4>

                <form class="needs-validation" m action="forumAddPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" required>
                    </div>
                    <?php/* $sql = "SELECT * FROM typecho_metas WHERE type = 'category' AND parent = 0 ORDER BY mid DESC";
                $result = $connect->query($sql);
                ?>

                <div class="form-group col-sm-4">
                    <label for="validationCustom01">分类</label>
                    <select class="form-control" id="example-select" name="category" onchange="loadSubCategories(this.value)">
                        <option value="">请选择</option>
                        <?php
                        if ($result->num_rows > 0) {
                            while ($row = $result->fetch_assoc()) {
                                echo '<option value="' . $row["mid"] . '">' . $row["name"] . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>
                
                <div id="subCategoriesContainer"></div> */?>
                <?php  
                function displayCategories($parent = 0, $level = 1) {  
                    global $connect;  
                    $query = "SELECT * FROM typecho_forum_section WHERE parent = $parent ORDER BY id DESC";  
                    $result = mysqli_query($connect, $query);  
                  
                    while ($category = mysqli_fetch_array($result)) {  
                        // Skip the top-level categories  
                        if ($level === 1) {  
                            continue; // Skip the current iteration  
                        }  
                          
                        echo '<option value="' . $category['id'] . '">' . str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;', $level - 1) . $category['name'] . '</option>';  
                        if ($level < 3) {  
                            displayCategories($category['id'], $level + 1);  
                        }  
                    }  
                }  
                  
                $withdrawals = "SELECT * FROM typecho_forum_section WHERE parent = 0 ORDER BY id DESC";  
                $withdrawalsResult = mysqli_query($connect, $withdrawals);  
                ?>  
                <div class="form-group col-sm-4">  
                    <label for="validationCustom01">选择圈子</label>  
                    <select class="form-control" id="example-select" name="section">  
                        <?php  
                        // Fetch and display top-level categories, but make them disabled  
                        while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {  
                            echo '<option value="'.$withdrawal['id'].'" disabled>'.$withdrawal['name'].'</option>';  
                            displayCategories($withdrawal['id'], 2); // Start from level 2 for subcategories  
                        }  
                        ?>  
                    </select>  
                </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">帖子标题</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入标题"
                               name="articletitle" required>
                    </div>
                     <label for="validationCustom01">帖子内容</label>
                      
                        <div id="editor—wrapper">
                            <div id="toolbar-container"></div>
                            <div id="editor-container"></div>
                             <textarea id="editorContent" name="articletext" style="display: none;"></textarea>  
                        </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="forumAddPost">发布帖子</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>
<link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
<script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>
<script>
const { createEditor, createToolbar } = window.wangEditor
const editorConfig = {
    placeholder: '请输入内容...',
    onChange(editor) {
      const html = editor.getHtml();  
    document.getElementById('editorContent').value = html;  
    },  
}
<?php 
$edithOf = false; 
include_once 'editor.php';
?>
<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->
<script>
    function loadSubCategories(parentId) {
        var container = document.getElementById("subCategoriesContainer");
        container.innerHTML = ""; // 清空容器
        
        if (parentId !== "") {
            var xmlhttp = new XMLHttpRequest();
            xmlhttp.onreadystatechange = function() {
                if (this.readyState == 4 && this.status == 200) {
                    container.innerHTML = this.responseText;
                }
            };
            xmlhttp.open("GET", "getSubCategories.php?parentId=" + parentId, true);
            xmlhttp.send();
        }
    }
</script>
<script>
    function check() {
        let title = document.getElementsByName('articletitle')[0].value.trim();
        let text = document.getElementsByName('articletext')[0].value.trim();
        if (title.length == 0) {
            alert("帖子标题不能为空");
            return false;
        } else if (text.length == 0) {
            alert("帖子内容不能为空");
            return false;
        }

    }

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>