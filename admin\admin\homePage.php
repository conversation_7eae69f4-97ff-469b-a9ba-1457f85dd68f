<?php
session_start();
?>



<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_pages";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">首页设置</h4>
                <form class="needs-validation" action="homePagePost.php" method="post" onsubmit="return check()" novalidate>
                    <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">首页模式</label>
                        <select class="form-control" id="example-select" name="homeMode">
                            <?php
                            if ($row['homeMode']==1) {
                                echo '<option value="1" selected>文章模式</option>
                                      <option value="2">帖子模式</option>';
                            }else{
                                echo '<option value="1">文章模式</option>
                                      <option value="2" selected>帖子模式</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    
                    
                    <div class="form-group mb-3">
                      <label for="notice">首页公告：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                      <textarea id="notice" class="form-control" name="Announcement" rows="5"><?php echo $row['Announcement']; ?></textarea>
                    </div>
                    <div class="form-group col-sm-4">
                      <label for="Displaytime">公告显示间隔：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">一天 = 86400000毫秒</span>
                      <div class="d-flex align-items-center">
                        <input name="Displaytime" class="form-control" type="number" required="" id="Displaytime" placeholder="单位：毫秒" style="flex: 1;" value="<?php echo $row['Displaytime']; ?>">
                        <span style="margin-left: 10px;">毫秒</span>
                      </div>
                    </div>
                    <div class="form-group mb-3">
                          <label for="notice">推荐搜索词（用“|”分割）: </label>
                    
                          <input name="Searchtext" class="form-control" type="text" required="" id="notice" placeholder="推荐搜索词" value="<?php echo $row['Searchtext']; ?>">
                    
                    </div>
                     <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler4(obj) {
                                var input = document.getElementById("switch4");
                                var PaymentMethods = document.getElementById("PaymentMethods");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("滚动公告打开");
                                    input.value = "1";
                                    PaymentMethods.style.display = "block";
                                } else {
                                    console.log("滚动公告关闭");
                                    input.value = "0";
                                    PaymentMethods.style.display = "none";
                                }
                            }
                        </script>
                        <label for="validationCustom01">滚动公告开关</label>
                            <?php
                            if ($row['Noticeswitch']==1) {
                                echo '<input type="checkbox" name="Noticeswitch" id="switch4" value="1" data-switch="success"
                               onclick="myOnClickHandler4(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Noticeswitch" id="switch4" value="0" data-switch="success"
                               onclick="myOnClickHandler4(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch4" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <?php
                    if ($row['Noticeswitch']==1) {
                        echo '<div id="PaymentMethods" style="display: block;">';
                    } else {
                        echo '<div id="PaymentMethods" style="display: none;">';
                    }
                    ?>
                    <div class="form-group mb-3">
                      <label for="notice">滚动公告：</label>
                      <textarea id="notice" class="form-control" name="Notice" rows="3"><?php echo $row['Notice']; ?></textarea>
                    </div>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler2(obj) {
                            var input = document.getElementById("switch1");
                            console.log(input);
                            if (obj.checked) {
                                console.log("帖子轮播图打开");
                                input.value = "1";
                            } else {
                                console.log("帖子轮播图关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">首页轮播图开关</label>
                    <?php
                    if ($row['Carouselswitch']==1) {
                        echo '<input type="checkbox" name="Carouselswitch" id="switch1" value="1" data-switch="success"
                           onclick="myOnClickHandler2(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="Carouselswitch" id="switch1" value="0" data-switch="success"
                           onclick="myOnClickHandler2(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch1" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">轮播图样式</label>
                        <select class="form-control" id="example-select" name="swiperStyle">
                            <?php
                            if ($row['swiperStyle']=='true') {
                                echo '<option value="true" selected>多图模式</option>
                                      <option value="false">单图模式</option>';
                            }else{
                                echo '<option value="true">多图模式</option>
                                      <option value="false" selected>单图模式</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                   <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler3(obj) {
                            var input = document.getElementById("switch2");
                            console.log(input);
                            if (obj.checked) {
                                console.log("图标模块打开");
                                input.value = "1";
                            } else {
                                console.log("图标模块关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">图标模块开关</label>
                    <?php
                    if ($row['Iconswitch']==1) {
                        echo '<input type="checkbox" name="Iconswitch" id="switch2" value="1" data-switch="success"
                           onclick="myOnClickHandler3(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="Iconswitch" id="switch2" value="0" data-switch="success"
                           onclick="myOnClickHandler3(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch2" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler22(obj) {
                            var input = document.getElementById("switch21");
                            console.log(input);
                            if (obj.checked) {
                                console.log("置顶帖子打开");
                                input.value = "1";
                            } else {
                                console.log("置顶帖子关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">置顶文章开关（文章模式下生效）</label>
                    <?php
                    if ($row['Hometop']==1) {
                        echo '<input type="checkbox" name="Hometop" id="switch21" value="1" data-switch="success"
                           onclick="myOnClickHandler22(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="Hometop" id="switch21" value="0" data-switch="success"
                           onclick="myOnClickHandler22(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch21" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">置顶文章排版（文章模式下生效）</label>
                        <select class="form-control" id="example-select" name="topStyle">
                            <?php
                            if ($row['topStyle']==1) {
                                echo '<option value="1" selected>经典</option>
                                      <option value="2">唯美</option>';
                            }else{
                                echo '<option value="1">经典</option>
                                      <option value="2" selected>唯美</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                     <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">首页文章排版（文章模式下生效）</label>
                        <select class="form-control" id="example-select" name="actStyle">
                            <?php
                            if ($row['actStyle']==1) {
                                echo '<option value="1" selected>经典</option>
                                      <option value="2">唯美</option>
                                      <option value="3">瀑布流</option>';
                            }else if($row['actStyle']==2){
                                echo '<option value="1">经典</option>
                                      <option value="2" selected>唯美</option>
                                      <option value="3">瀑布流</option>';
                            }else{
                                 echo '<option value="1">经典</option>
                                         <option value="2">唯美</option>
                                      <option value="3" selected>瀑布流</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                   
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler5(obj) {
                            var input = document.getElementById("switch5");
                            console.log(input);
                            if (obj.checked) {
                                console.log("按钮模块打开");
                                input.value = "1";
                            } else {
                                console.log("按钮模块关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">精选圈子开关（帖子模式下生效）</label>
                    <?php
                    if ($row['circleOf']==1) {
                        echo '<input type="checkbox" name="circleOf" id="switch5" value="1" data-switch="success"
                           onclick="myOnClickHandler5(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="circleOf" id="switch5" value="0" data-switch="success"
                           onclick="myOnClickHandler5(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch5" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="homePagePost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->




<?php
include_once 'Footer.php';
?>

</body>
</html>