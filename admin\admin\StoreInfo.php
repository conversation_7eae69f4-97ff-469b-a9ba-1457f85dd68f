<?php
session_start();
?>

<?php
include_once 'Nav.php';
$fid = intval($_GET['fid']); // Convert fid to an integer
$type = $_GET['type'];
$downurl = $_GET['downurl'];
$imageurl = $_GET['imageurl'];
$author = $_GET['author'];
$cp = $_GET['cp'];
$price = $_GET['price'];

?>
<style>
    .theme-card img {
        border-radius: 20px;
        width: 100%;
        object-fit: cover;
    }
   .theme-card-author {
        font-size: 0.9rem;
        color: #888;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .card-author {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .card-price {
        font-size: 16px;
        font-weight: bold;
    }
    .price-buy {
        color: #ff6a6a;
    }
    .price-free {
        color: #0acf97;
    }
</style>
<div class="row">
    <div class="col-lg-12">
        <div class="alert alert-danger alert-dismissible bg-danger text-white border-0 fade show" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <strong>注意</strong> 除官方外的插件、主题都属于第三方开发，官方不保证所有第三方的更新、售后服务，并且可能存在未知BUG、部分官方功能失效的风险，购买前请自己谨慎辨别，购买后不支持退款！
        </div>
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">
                <?php echo $cp;?>
                </h4>
                <div>
                    <?php
                    if($downurl!=''&&$type=='plugin'){
                        echo ' <a class="fabu" href="'.$downurl.'" target="_blank">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded" style="margin-bottom: 20px;">
                            <i class="dripicons-download mr-1"></i><span>下载插件</span>
                        </button>
                        </a>';
                    }
                   ?>
                </div>
                <div class="theme-card-author">
                <div class="card-author">
                    作者：<?php echo $author;?>
                </div>
                
                    <?php 
                    if($price=='0'){
                        echo '<div class="card-price price-free">免费</div>';
                    }else{
                        echo '<div class="card-price price-buy">'.$price.'￥</div>';
                    }
                    ?>
                
                </div>
                 <div class="theme-card">
                    <img src="<?php echo $imageurl;?>">
                </div>
                <hr />
                <div id="post-details"></div>
            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>

<link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
<script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var fid = <?php echo $fid; ?>;

        fetch(`https://starapi.qxzhi.cn/SProForum/postInfo?id=${fid}`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 1) {
                    var postData = data.data;
                    var postHtml = postData.text;

                    // Replace all <img> tags with <img style="width:100%">
                    postHtml = postHtml.replace(/<img/g, '<img style="width:100%"');

                    // Set the modified content back to the post-details div
                    document.getElementById('post-details').innerHTML = postHtml;
                } else {
                    document.getElementById('post-details').innerHTML = '<p>详情获取失败</p>';
                }
            })
            .catch(error => {
                document.getElementById('post-details').innerHTML = '<p>请求失败</p>';
            });
    });
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>
