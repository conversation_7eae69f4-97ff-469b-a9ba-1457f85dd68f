(window.webpackJsonp=window.webpackJsonp||[]).push([[2],{437:function(t,n,e){},438:function(t,n,e){"use strict";e(437)},439:function(t,n,e){"use strict";e.r(n);e(35);var o,l=e(28),r=(e(75),{layout:"no",head:function(){return{title:"StarProApi",titleTemplate:"%s - 为简约而生"}},data:function(){return{version:20,versionText:"2.0",key:"",isGx:!1,goSystem:!1,newVersion:"获取中...",newVersionCode:0,installStatus:0,goInstall:!1,installData:"",isTypecho:!1,goInstallTypecho:!1,name:"",password:"",isPro:0,Faz:0}},beforeDestroy:function(){},created:function(){this.isPro=this.$api.isPro()},mounted:function(){this.isOut(),this.isInstall()},methods:(o={isOut:function(){var t=this.$api.vlOser()+this.$api.v1Oser();console.log(t)},goHelp:function(){window.open(this.$api.docUrl(),"_blank")},isInstall:function(){var t=this;t.$axios.$get(t.$api.isInstall()).then((function(n){n.code,100==n.code&&t.$alert("Redis连接失败或未安装！请检查您的API配置或Redis安装状态。","API警告",{confirmButtonText:"我知道了",type:"warning"}),101==n.code&&(t.isTypecho=!0),102==n.code&&t.$alert("Mysql数据库连接失败或未安装，请检查您的API配置或Mysql安装状态。","API警告",{confirmButtonText:"我知道了",type:"warning"})})).catch((function(n){console.log(n),t.$message({message:"接口请求异常，请检查数据库连接或设备网络！",type:"error"})}))},goTypecho:function(){var t=this;t.installData="",t.installStatus=0,t.name="",t.password="",t.goInstallTypecho=!0,t.Faz=1},installTypecho:function(){var t=this;t.key;""!=t.name&&""!=t.password||t.$message({message:"请输入正确的参数",type:"error"});var data={webkey:t.key,name:t.name,password:t.password};t.installStatus=1,t.$axios.$post(t.$api.typechoInstall(),this.qs.stringify(data),{progress:!1}).then((function(n){t.installStatus=2,t.installData=n.msg})).catch((function(n){t.installStatus=2,t.installData="网络错误，请重试！"}))},Install:function(){var t=this;t.installStatus=0,1==t.Faz?t.goInstall=!0:(t.$message({message:"请先完成第一步安装！",type:"error"}),t.isTypecho=!0)}},Object(l.a)(o,"isInstall",(function(){var t=this;t.$axios.$get(t.$api.isInstall()).then((function(n){n.code,100==n.code&&t.$alert("Redis连接失败或未安装！请检查您的API配置或Redis安装状态。","API警告",{confirmButtonText:"我知道了",type:"warning"}),101==n.code&&(t.isTypecho=!0),102==n.code&&t.$alert("Mysql数据库连接失败或未安装，请检查您的API配置或Mysql安装状态。","API警告",{confirmButtonText:"我知道了",type:"warning"})})).catch((function(n){console.log(n),t.$message({message:"接口请求异常，请检查数据库连接或设备网络！",type:"error"})}))})),Object(l.a)(o,"goInstallStart",(function(){var t=this,data=(t.key,{webkey:t.key});t.installStatus=1,t.$axios.$post(t.$api.newInstall(),this.qs.stringify(data),{progress:!1}).then((function(n){t.installStatus=2,t.installData=n,1==t.isPro&&setTimeout((function(){t.installData="正在进行第三步安装，请稍等...",t.goInstallPro()}),500)})).catch((function(n){t.installStatus=2,t.installData="常规更新异常，请重试！"}))})),Object(l.a)(o,"goInstallPro",(function(){var t=this,data=(t.key,{webkey:t.key});t.$axios.$post(t.$api.proInstall(),this.qs.stringify(data),{progress:!1}).then((function(n){t.installData=n.msg})).catch((function(n){t.installData="第三步安装异常，请重试！"}))})),Object(l.a)(o,"loginSystem",(function(){var t=this,data=(t.key,{webkey:t.key});t.$axios.$post(t.$api.isKey(),this.qs.stringify(data)).then((function(n){1==n.code?(t.$message({message:n.msg,type:"success"}),t.goSystem=!1,localStorage.setItem("webkey",t.key),t.$router.push({path:"/system/home"})):t.$message({message:n.msg,type:"error"})})).catch((function(n){console.log(n),t.$message({message:"接口请求异常，请检查网络！",type:"error"})}))})),o)}),c=(e(438),e(29)),component=Object(c.a)(r,(function(){var t=this,n=t._self._c;return n("div",{staticClass:"container"},[n("div",{staticClass:"index-main"},[n("div",{staticStyle:{"margin-top":"70px"}},[n("span",{staticClass:"star-logo1 font50"},[t._v("StarProApi")]),n("span",{staticClass:"star-logo3"},[t._v(t._s(t.versionText))])]),t._v(" "),n("div",{staticClass:"rule-version"},[n("br"),t._v(" "),n("div",{staticClass:"rule-btn"},[n("el-row",[n("span",{staticClass:"star-button1",attrs:{round:""},on:{click:function(n){return t.Install()}}},[t._v("安装/更新")]),t._v(" "),n("span",{staticClass:"star-button2",attrs:{round:""},on:{click:function(n){return t.goHelp()}}},[t._v("官方文档")])]),t._v(" "),n("el-row",[n("div",{staticClass:"protocol"},[n("a",{attrs:{href:"https://www.yuque.com/senyun-ev0j3/starpro/esvckfg8h71cv7wu",target:"_blank"}},[t._v("许可协议 / 免责申明")])])])],1)]),t._v(" "),n("el-dialog",{attrs:{title:"进行第二步安装",visible:t.goInstall,"close-on-click-modal":!1,width:"400px"},on:{"update:visible":function(n){t.goInstall=n}}},[0==t.installStatus?n("div",{staticClass:"dialog-form"},[n("el-input",{attrs:{placeholder:"验证管理Key",type:"password"},model:{value:t.key,callback:function(n){t.key=n},expression:"key"}})],1):t._e(),t._v(" "),1==t.installStatus?n("div",{staticClass:"install-loading"},[n("p",[t._v("正在执行第二步安装，请勿关闭窗口或刷新页面！")])]):t._e(),t._v(" "),2==t.installStatus?n("div",{staticClass:"install-data"},[n("div",{staticClass:"install-data-text"},[t._v("\n            "+t._s(t.installData)+"\n          ")])]):t._e(),t._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[0==t.installStatus?n("el-button",{attrs:{round:""},on:{click:function(n){t.goInstall=!1}}},[t._v("取 消")]):t._e(),t._v(" "),0==t.installStatus?n("el-button",{attrs:{round:"",type:"primary"},on:{click:function(n){return t.goInstallStart()}}},[t._v("确 定")]):t._e(),t._v(" "),1==t.installStatus?n("el-button",{attrs:{round:"",type:"primary",loading:!0}},[t._v("执行中")]):t._e(),t._v(" "),2==t.installStatus?n("el-button",{attrs:{round:"",type:"danger"},on:{click:function(n){t.installStatus=0}}},[t._v("重 试")]):t._e(),t._v(" "),2==t.installStatus?n("el-button",{attrs:{round:"",type:"primary"},on:{click:function(n){t.goInstall=!1}}},[t._v("完 成")]):t._e()],1)]),t._v(" "),n("el-dialog",{attrs:{title:"开始第一步安装",visible:t.isTypecho,width:"400px","close-on-click-modal":!1},on:{"update:visible":function(n){t.isTypecho=n}}},[t.goInstallTypecho?t._e():[n("span",[t._v("请确认你的操作")])],t._v(" "),t.goInstallTypecho?[0==t.installStatus?n("div",{staticClass:"dialog-form"},[n("el-input",{attrs:{placeholder:"验证管理Key",type:"password"},model:{value:t.key,callback:function(n){t.key=n},expression:"key"}}),t._v(" "),t.isGx?t._e():n("el-input",{attrs:{placeholder:"请输入APP管理用户账号",type:"text"},model:{value:t.name,callback:function(n){t.name=n},expression:"name"}}),t._v(" "),t.isGx?t._e():n("el-input",{attrs:{placeholder:"请输入APP管理用户密码",type:"text"},model:{value:t.password,callback:function(n){t.password=n},expression:"password"}})],1):t._e(),t._v(" "),1==t.installStatus?n("div",{staticClass:"install-loading"},[n("p",[t._v("正在执行第一步安装，请勿关闭窗口或刷新页面！")])]):t._e(),t._v(" "),2==t.installStatus?n("div",{staticClass:"install-data"},[n("div",{staticClass:"install-data-text"},[t._v("\n              "+t._s(t.installData)+"\n            ")])]):t._e()]:t._e(),t._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t.goInstallTypecho?t._e():[n("el-button",{attrs:{round:"",type:"danger"},on:{click:function(n){return t.goTypecho()}}},[t._v("开始安装")]),t._v(" "),n("el-button",{attrs:{round:"",type:"primary"},on:{click:function(n){t.isGx=!0,t.goTypecho()}}},[t._v("仅更新")]),t._v(" "),n("el-button",{attrs:{round:"",type:"blue"},on:{click:function(n){t.isTypecho=!1}}},[t._v("再等等")])],t._v(" "),t.goInstallTypecho?[0==t.installStatus?n("el-button",{attrs:{round:""},on:{click:function(n){t.isTypecho=!1}}},[t._v("取 消")]):t._e(),t._v(" "),0==t.installStatus?n("el-button",{attrs:{round:"",type:"primary"},on:{click:function(n){return t.installTypecho()}}},[t._v("确 定")]):t._e(),t._v(" "),1==t.installStatus?n("el-button",{attrs:{round:"",type:"primary",loading:!0}},[t._v("执行中")]):t._e(),t._v(" "),2==t.installStatus?n("el-button",{attrs:{round:"",type:"danger"},on:{click:function(n){t.installStatus=0}}},[t._v("重 试")]):t._e(),t._v(" "),2==t.installStatus?n("el-button",{attrs:{round:"",type:"primary"},on:{click:function(n){t.isTypecho=!1,t.goInstall=!0,t.goInstallStart()}}},[t._v("继续安装")]):t._e()]:t._e()],2)],2)],1)])}),[],!1,null,null,null);n.default=component.exports}}]);