<?php
error_reporting(0);
header("Content-Type:text/html; charset=utf8");

//StarApi站点地址
$api_site    = "http://127.0.0.1:997/";//最后要带“/” 例如https://baidu.com/
$api_key     = "123456";//搭建api时设置的key

//数据库配置
$db_address  = "localhost";//数据库地址
$db_username = "admin";//数据库用户名
$db_name     = "admin";//数据库名 （默认与数据库用户名相同）
$db_password = "2XM4wW3Ns4ZN2L2X";//数据库密码

//redis配置
$redis_password = '';//redis密码 默认为空
$redis_host = '127.0.0.1';//redis地址 默认127.0.0.1
$redis_port = 6379;//redis端口 默认6379;


//以下勿动  以下勿动    以下勿动    以下勿动
$API_IS_INSTALL = $api_site.'installStar/isInstall';  
$API_TYPECHO_INSTALL = $api_site.'installStar/typechoInstall';  
$API_TO_UTF8MB4 = $api_site.'installStar/toUtf8mb4';  
$API_NEW_VERSION = $api_site.'systemStarPro/apiNewVersion';  
$API_NEW_INSTALL = $api_site.'installStar/newInstall';  
$API_PRO_INSTALL = $api_site.'installStar/proInstall';  
$API_IS_KEY = $api_site.'systemStarPro/isKey';  
$API_GET_CONFIG = $api_site.'systemStarPro/getConfig';  
$API_GET_API_CONFIG = $api_site.'systemStarPro/getApiConfig';  
$API_CONFIG_UPDATE = $api_site.'systemStarPro/apiConfigUpdate';  
$API_SETUP_MYSQL = $api_site.'systemStarPro/setupMysql';  
$API_SETUP_REDIS = $api_site.'systemStarPro/setupRedis';  
$API_SETUP_CACHE = $api_site.'StarFreeSystem/setupCache';  
$API_SETUP_EMAIL = $api_site.'systemStarPro/setupEmail';  
$API_SETUP_WEB_KEY = $api_site.'systemStarPro/setupWebKey';  
$API_SETUP_CONFIG = $api_site.'systemStarPro/setupConfig';  
$API_ALL_CONFIG = $api_site.'systemStarPro/allConfig';  
$API_ADD_VIP_TYPE = $api_site.'systemStarPro/addVipType';  
$API_UPDATE_VIP_TYPE = $api_site.'systemStarPro/updateVipType';  
$API_DELETE_VIP_TYPE = $api_site.'systemStarPro/deleteVipType';  
$API_VIP_TYPE_LIST = $api_site.'systemStarPro/vipTypeList';  
$API_ADD_APP = $api_site.'systemStarPro/addApp';  
$API_UPDATE_APP = $api_site.'systemStarPro/updateApp';  
$API_DELETE_APP = $api_site.'systemStarPro/deleteApp'; 
$API_APP_LIST = $api_site.'systemStarPro/appList'; 
$API_GET_EMAIL_TEMPLATE_CONFIG = $api_site.'systemStarPro/getEmailTemplateConfig'; 
$API_EMAIL_TEMPLATE_CONFIG_UPDATE = $api_site.'systemStarPro/emailTemplateConfigUpdate'; 
$API_UPLOAD_FULL = $api_site.'upload/full'; 

