<?php
session_start();
?>



<?php
include_once 'Nav.php';

$sql2 = "SELECT * FROM Sy_set";
$result2 = mysqli_query($connect, $sql2);
if (mysqli_num_rows($result2) > 0) {
    $row2 = mysqli_fetch_assoc($result2);
}

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));
$response = curl_exec($curl);
$responseData = json_decode($response, true);  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $adsVideoType = $responseData['data']['adsVideoType'];    
    $adsSecuritykey = $responseData['data']['adsSecuritykey'];  
    $pushAdsPrice = $responseData['data']['pushAdsPrice'];  
    $pushAdsNum = $responseData['data']['pushAdsNum'];
    $bannerAdsPrice = $responseData['data']['bannerAdsPrice'];  
    $bannerAdsNum = $responseData['data']['bannerAdsNum'];  
    $startAdsPrice = $responseData['data']['startAdsPrice'];  
    $startAdsNum = $responseData['data']['startAdsNum'];  
    $adsGiftNum = $responseData['data']['adsGiftNum']; 
    $adsGiftAward = $responseData['data']['adsGiftAward']; 

} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">广告配置</h4>
                <form class="needs-validation" action="setAdPost.php" method="post"
                      novalidate>
                    <div class="form-group mb-3">
                          <p>在这里设置付费广告位和广告联盟的激励广告配置，对于付费广告位为了用户体验考虑，请设置为推荐的数量。对于广告联盟，推荐选择服务端回调来防止用户刷<?php echo $row2['Assetname'];  ?>等情况。</p>
                    </div>
                     <div class="form-group col-sm-4">
                        <label for="adsVideoType">回调模式<span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              选择激励广告回调模式
                          </span></label>
                            <select class="form-control" id="adsVideoType" name="adsVideoType">
                                <?php
                                $regions = [
                                    "0" => "前端回调",
                                    "1" => "服务器端回调"
                                ];
                                
                                foreach ($regions as $key => $value) {
                                    $selected = ($adsVideoType == $key) ? "selected" : "";
                                    echo "<option value=\"$key\" $selected>$value</option>";
                                }
                                ?>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                          <label for="adsSecuritykey">UniAd安全码
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              只有服务端回调模式生效，<a href="https://uniad.dcloud.net.cn/" target="_blank">uni-ad 广告联盟</a>配置完成后的Security key
                          </span></label>
                          <input name="adsSecuritykey" class="form-control" type="text" id="adsSecuritykey" placeholder="请输入UniAd安全码" value="<?php echo $adsSecuritykey;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="adsGiftNum">激励广告次数
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              只有前端回调模式生效，用户每天最多可发起激励广告次数
                          </span></label>
                          <input name="adsGiftNum" class="form-control" type="number" id="adsGiftNum" placeholder="请输入激励广告次数" value="<?php echo $adsGiftNum;  ?>" required>
                    </div>
                    <div class="form-group mb-3">
                          <label for="adsGiftAward">激励广告奖励<?php echo $row2['Assetname'];  ?>
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                             对接广告联盟，用户每次浏览激励广告的<?php echo $row2['Assetname'];  ?>
                          </span></label>
                          <input name="adsGiftAward" class="form-control" type="number" id="adsGiftAward" placeholder="请输入激励广告奖励<?php echo $row2['Assetname'];  ?>" value="<?php echo $adsGiftAward;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="pushAdsPrice">内容推流广告价格
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              （<?php echo $row2['Assetname'];  ?>/天）
                          </span></label>
                          <input name="pushAdsPrice" class="form-control" type="number" id="pushAdsPrice" placeholder="请输入内容推流广告价格" value="<?php echo $pushAdsPrice;  ?>" required>
                    </div>
                    <div class="form-group mb-3">
                          <label for="pushAdsNum">内容推流广告位数量
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              随机在文章、帖子列表加载刷新时显示，建议数量为10
                          </span></label>
                          <input name="pushAdsNum" class="form-control" type="number" id="pushAdsNum" placeholder="请输入内容推流广告位数量" value="<?php echo $pushAdsNum;  ?>" required>
                    </div>
                    
                    <div class="form-group mb-3">
                          <label for="bannerAdsPrice">横幅广告价格
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              （<?php echo $row2['Assetname'];  ?>/天）
                          </span></label>
                          <input name="bannerAdsPrice" class="form-control" type="number" id="bannerAdsPrice" placeholder="请输入横幅广告价格" value="<?php echo $bannerAdsPrice;  ?>" required>
                    </div>
                    <div class="form-group mb-3">
                          <label for="bannerAdsNum">横幅广告位数量
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              在APP不同位置随机展现（比如帖子详情页下方），建议数量为5
                          </span></label>
                          <input name="bannerAdsNum" class="form-control" type="number" id="bannerAdsNum" placeholder="请输入横幅广告位数量" value="<?php echo $bannerAdsNum;  ?>" required>
                    </div>
                    
                     <div class="form-group mb-3">
                          <label for="startAdsPrice">启动图广告价格
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              （<?php echo $row2['Assetname'];  ?>/天）
                          </span></label>
                          <input name="startAdsPrice" class="form-control" type="number" id="startAdsPrice" placeholder="请输入横幅广告价格" value="<?php echo $startAdsPrice;  ?>" required>
                    </div>
                    <div class="form-group mb-3">
                          <label for="startAdsNum">启动图广告位数量
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              进入APP时会展现的广告，建议数量为1
                          </span></label>
                          <input name="startAdsNum" class="form-control" type="number" id="startAdsNum" placeholder="请输入横幅广告位数量" value="<?php echo $startAdsNum;  ?>" required>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="setAdPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->




<?php
include_once 'Footer.php';
?>

</body>
</html>