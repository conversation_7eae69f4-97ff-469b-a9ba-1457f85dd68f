<?php
session_start();
?>
<?php

include_once 'connect.php';
$name = $_POST['name'];
$slug = $_POST['slug'];
$type = $_POST['type'];
$parent = $_POST['parent'];
$description = $_POST['description'];
$imgurl = $_POST['imgurl'];
$order = $_POST['order'];
if (empty($imgurl)) {
    $imgurl = NULL;
}
if ($type=='tag') {
    $parent = '0';
}
if (empty($order)) {
    $order = '0';
}
if (empty($description)) {
    $description = NULL;
}
if (empty($parent)) {
    $parent = '0';
}

    


$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $checkQuery = "SELECT * FROM typecho_metas WHERE slug = '$slug'";
    $checkStmt = mysqli_query($connect, $checkQuery);
    if (mysqli_num_rows($checkStmt) > 0) {
        echo "<script>alert('缩略名已存在，请重新输入');history.back();;</script>";
    } else {
        $charu = "insert into typecho_metas (`name`,`slug`,`type`,`parent`,`description`,`imgurl`,`order`) values ('$name','$slug','$type','$parent','$description','$imgurl','$order')";
        $result = mysqli_query($connect, $charu);
        if ($result) {
                echo "<script>alert('创建成功');location.href = 'metaAdmin.php';</script>";
            } else {
                echo "<script>alert('".$name.
$slug.
$type.
$parent.

$description.
$imgurl.
$order."');history.back();</script>";   
            }
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
