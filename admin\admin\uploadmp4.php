<?php
session_start();

// 获取当前时间戳
$time = time();

// 获取上传的文件名
$file = basename($_SERVER['PHP_SELF']);

// 检查用户是否已登录
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] !== '') {
    include_once 'connect.php';

    // 检查是否有文件上传错误
    if ($_FILES["video"]["error"] > 0) {
        echo $_FILES["video"]["error"];
        exit;
    }

    // 获取上传的视频文件信息
    $videoName = $_FILES["video"]["name"];
    $videoType = $_FILES["video"]["type"];
    $videoSize = $_FILES["video"]["size"];
    $videoTmp = $_FILES["video"]["tmp_name"];

    // 指定视频文件的保存路径和文件名
    $savePath = "img/";
    $videoPath = $savePath . $videoName;
    $uploadUrl = $API_UPLOAD_FULL.'?webkey='.$api_key;
    // 直接上传视频文件到API接口
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => $uploadUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_POSTFIELDS => array('file'=> new CURLFILE($videoTmp)),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    // 解析上传接口的响应
    $responseData = json_decode($response, true);

    // 检查是否上传成功
    if (isset($responseData['errno']) && $responseData['errno'] === 0) {
        // 返回视频文件的链接
        $videoURL = $responseData['data']['url'];
        echo $videoURL;
    } else {
        echo "上传失败";
    }

} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
?>