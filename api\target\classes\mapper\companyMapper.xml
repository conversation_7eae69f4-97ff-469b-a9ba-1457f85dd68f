<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.companyDao">
    <resultMap id="BaseResultMap" type="com.StarProApi.entity.identifyCompany" >
        <result column="uid" property="uid" />
        <result column="regno" property="regno" />
        <result column="name" property="name" />
        <result column="entname" property="entname" />
        <result column="idcard" property="idcard" />
        <result column="identifyStatus" property="identifyStatus" />
    </resultMap>

    <sql id="base-field">
        tco.uid,
        tco.regno,
        tco.name,
        tco.entname,
        tco.idcard,
        tco.identifyStatus
    </sql>
    <sql id="Base_Column_List">
        `uid`,
        `regno`,
        `name`,
        `entname`,
        `idcard`,
        `identifyStatus`
    </sql>
    <update id="update" parameterType="com.StarProApi.entity.identifyCompany">
        update ${prefix}_company
        <set>
            <if test="null!=regno">regno = #{regno},</if>
            <if test="null!=name">name = #{name},</if>
            <if test="null!=entname">entname = #{entname},</if>
            <if test="null!=idcard">idcard = #{idcard},</if>
            <if test="null!=identifyStatus">identifyStatus = #{identifyStatus}</if>
        </set>
        <where>uid=#{uid} and regno = #{regno}</where>
    </update>

    <delete id="remove" >
        delete from ${prefix}_company
        where uid = #{key}
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_company
        WHERE `uid` = #{uid}
    </select>

    <select id="queryInfo" resultMap="BaseResultMap" parameterType="com.StarProApi.entity.identifyCompany">
        select <include refid="base-field"/>
            from ${prefix}_company tco
        <where>
            <if test="null != identifyCompany.uid">
                and tco.uid = #{identifyCompany.uid}
            </if>
            <if test="null != identifyCompany.idcard">
                and tco.idCard = #{identifyCompany.idcard}
            </if>
            <if test="null != identifyCompany.name">
                and tco.name = #{identifyCompany.name}
            </if>
            <if test="null != identifyCompany.identifyStatus">
                and tco.identifyStatus = #{identifyCompany.identifyStatus}
            </if>
            <if test="null != identifyCompany.entname">
                and tco.entname = #{identifyCompany.entname}
            </if>
            <if test="null != identifyCompany.regno">
                and tco.regno = #{identifyCompany.regno}
            </if>
        </where>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <select id="queryAll" resultMap="BaseResultMap">
        select *
            from ${prefix}_company
    </select>

    <insert id="insert" parameterType="com.StarProApi.entity.identifyCompany">
        insert into ${prefix}_company(
            uid,
            <if test="null!=regno">regno</if>,
            name,
            <if test="null!=entname">entname</if>,
            idcard,
            identifyStatus
                                 )
        values (
        #{uid},
        <choose><when test="regno != null">#{regno}</when><otherwise>""</otherwise></choose>,
        #{name},
        <choose><when test="entname != null">#{entname}</when><otherwise>""</otherwise></choose>,
        #{idcard},
        <choose><when test="identifyStatus != null">#{identifyStatus}</when><otherwise>'0'</otherwise></choose>
        )
    </insert>
</mapper>