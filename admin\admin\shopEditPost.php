

<?php
session_start();
?>
<?php
include_once 'connect.php';
$id = $_POST['id'];
$subtype = $_POST['subtype'];
$uid = $_POST['uid'];
$num = $_POST['num'];
$uid = $_POST['uid'];
$title = $_POST['title'];
$price = $_POST['price'];
$vipDiscount = $_POST['vipDiscount'];
$imgurl = $_POST['imgurl'];
$text = $_POST['text'];
$value = $_POST['value'];
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $article3 = "SELECT * FROM typecho_shoptype WHERE id='$subtype' limit 1";
    $resarticle3 = mysqli_query($connect, $article3);
    $mod3 = mysqli_fetch_array($resarticle3); //父级
    $sort = $mod3['parent'];
    $charu = "UPDATE typecho_shop SET uid = '$uid' , title = '$title', price = '$price', vipDiscount = '$vipDiscount' , imgurl = '$imgurl', text = '$text', value = '$value', subtype = '$subtype', num = '$num', sort = '$sort' WHERE id = '$id'";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('".$subtype."修改成功".$sort."');location.href = 'shopAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');location.href = 'shopAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
