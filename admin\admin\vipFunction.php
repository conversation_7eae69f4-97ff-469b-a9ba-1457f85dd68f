<?php
session_start();
?>



<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_functions";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">VIP设置</h4>
                <form class="needs-validation" action="vipFunctionPost.php" method="post" onsubmit="return check()" novalidate>
                    <!--<div class="form-group col-sm-4" style="width:70%">-->
                    <!--  <label for="Vipdiscount">VIP商城折扣：</label>-->
                    <!--  <div class="d-flex align-items-center">-->
                    <!--    <input name="Vipdiscount" class="form-control" type="number" required="" id="Vipdiscount" placeholder="0~10" style="flex: 1;" value="<?php //echo $row['Vipdiscount']; ?>">-->
                    <!--    <span style="margin-left: 15px;">折</span>-->
                    <!--  </div>-->
                    <!--</div>-->
                    <div class="form-group mb-3">
                      <label for="notice">VIP特权说明：</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">支持html</span>
                          <textarea id="notice" class="form-control" name="Vipprivilege" rows="6"><?php echo $row['Vipprivilege']; ?></textarea>
                    </div>
                   
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="vipFunctionPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->
<script>
function check() {
  let Vipdiscount = document.getElementsByName('Vipdiscount')[0].value.trim();
  
  if (Vipdiscount < 1 || Vipdiscount > 10) {
    // 显示提示信息
    alert('VIP商城折扣在0~10之间！');
    // 阻止表单的提交
    return false;
  }
}
</script>



<?php
include_once 'Footer.php';
?>

</body>
</html>