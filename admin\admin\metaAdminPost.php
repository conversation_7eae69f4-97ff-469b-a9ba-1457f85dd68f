<?php
session_start();
?>

<?php
include_once 'connect.php';
$mid = $_GET['mid'];
$status = $_GET['status'];
$file = $_SERVER['PHP_SELF'];

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($status === 'Tj') {
        $sql = "UPDATE typecho_metas SET isrecommend = '1' WHERE mid = '$mid'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('推荐成功');history.back();</script>";
        } else {
            echo "<script>alert('推荐失败')';history.back();</script>";
        }
    } else if($status === 'Qx'){
        $sql = "UPDATE typecho_metas SET isrecommend = '0' WHERE mid = '$mid'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('取消成功');history.back();</script>";
        } else {
            echo "<script>alert('取消失败');history.back();</script>";
        }
    } else if ($status === 'Del') {
        $sql = "DELETE FROM typecho_metas WHERE mid = '$mid'";
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('删除成功');history.back();</script>";
        } else {
            echo "<script>alert('删除失败')';history.back();</script>";
        }
    }  else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}