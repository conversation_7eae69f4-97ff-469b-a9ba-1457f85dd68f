<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoApiconfigDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoApiconfig" >
        <result column="id" property="id" />
        <result column="webinfoTitle" property="webinfoTitle" />
        <result column="webinfoUrl" property="webinfoUrl" />
        <result column="webinfoUploadUrl" property="webinfoUploadUrl" />
        <result column="webinfoAvatar" property="webinfoAvatar" />
        <result column="pexelsKey" property="pexelsKey" />
        <result column="scale" property="scale" />
        <result column="clock" property="clock" />
        <result column="vipPrice" property="vipPrice" />
        <result column="vipDay" property="vipDay" />
        <result column="vipDiscount" property="vipDiscount" />
        <result column="isEmail" property="isEmail" />
        <result column="isInvite" property="isInvite" />
        <result column="cosAccessKey" property="cosAccessKey" />
        <result column="cosSecretKey" property="cosSecretKey" />
        <result column="cosBucket" property="cosBucket" />
        <result column="cosBucketName" property="cosBucketName" />
        <result column="cosPath" property="cosPath" />
        <result column="cosPrefix" property="cosPrefix" />
        <result column="aliyunEndpoint" property="aliyunEndpoint" />
        <result column="aliyunAccessKeyId" property="aliyunAccessKeyId" />
        <result column="aliyunAccessKeySecret" property="aliyunAccessKeySecret" />
        <result column="aliyunAucketName" property="aliyunAucketName" />
        <result column="aliyunUrlPrefix" property="aliyunUrlPrefix" />
        <result column="aliyunFilePrefix" property="aliyunFilePrefix" />
        <result column="ftpHost" property="ftpHost" />
        <result column="ftpPort" property="ftpPort" />
        <result column="ftpUsername" property="ftpUsername" />
        <result column="ftpPassword" property="ftpPassword" />
        <result column="ftpBasePath" property="ftpBasePath" />
        <result column="alipayAppId" property="alipayAppId" />
        <result column="alipayPrivateKey" property="alipayPrivateKey" />
        <result column="alipayPublicKey" property="alipayPublicKey" />
        <result column="alipayNotifyUrl" property="alipayNotifyUrl" />

        <result column="wxAppId" property="wxAppId" />
        <result column="wxAppSecret" property="wxAppSecret" />
        <result column="appletsAppid" property="appletsAppid" />
        <result column="appletsSecret" property="appletsSecret" />
        <result column="qqAppletsAppid" property="qqAppletsAppid" />
        <result column="qqAppletsSecret" property="qqAppletsSecret" />
        <result column="wxpayAppId" property="wxpayAppId" />
        <result column="wxpayMchId" property="wxpayMchId" />
        <result column="wxpayKey" property="wxpayKey" />
        <result column="wxpayNotifyUrl" property="wxpayNotifyUrl" />
        <result column="auditlevel" property="auditlevel" />
        <result column="forbidden" property="forbidden" />
        <result column="fields" property="fields" />

        <result column="pushAdsPrice" property="pushAdsPrice" />
        <result column="pushAdsNum" property="pushAdsNum" />
        <result column="bannerAdsPrice" property="bannerAdsPrice" />
        <result column="bannerAdsNum" property="bannerAdsNum" />
        <result column="startAdsPrice" property="startAdsPrice" />
        <result column="startAdsNum" property="startAdsNum" />

        <result column="epayUrl" property="epayUrl" />
        <result column="epayPid" property="epayPid" />
        <result column="epayKey" property="epayKey" />
        <result column="epayNotifyUrl" property="epayNotifyUrl" />
        <result column="mchSerialNo" property="mchSerialNo" />
        <result column="mchApiV3Key" property="mchApiV3Key" />
        <result column="cloudUid" property="cloudUid" />
        <result column="cloudUrl" property="cloudUrl" />
        <result column="pushAppId" property="pushAppId" />
        <result column="pushAppKey" property="pushAppKey" />
        <result column="pushMasterSecret" property="pushMasterSecret" />
        <result column="disableCode" property="disableCode" />
        <result column="allowDelete" property="allowDelete" />
        <result column="contentAuditlevel" property="contentAuditlevel" />
        <result column="isPush" property="isPush" />

        <result column="uploadLevel" property="uploadLevel" />
        <result column="clockExp" property="clockExp" />
        <result column="reviewExp" property="reviewExp" />
        <result column="postExp" property="postExp" />
        <result column="violationExp" property="violationExp" />
        <result column="deleteExp" property="deleteExp" />
        <result column="spaceMinExp" property="spaceMinExp" />
        <result column="chatMinExp" property="chatMinExp" />

        <result column="qiniuDomain" property="qiniuDomain" />
        <result column="qiniuAccessKey" property="qiniuAccessKey" />
        <result column="qiniuSecretKey" property="qiniuSecretKey" />
        <result column="qiniuBucketName" property="qiniuBucketName" />

        <result column="codeAccessKeyId" property="codeAccessKeyId" />
        <result column="codeAccessKeySecret" property="codeAccessKeySecret" />
        <result column="codeEndpoint" property="codeEndpoint" />
        <result column="codeTemplate" property="codeTemplate" />
        <result column="codeSignName" property="codeSignName" />
        <result column="isPhone" property="isPhone" />

        <result column="silenceTime" property="silenceTime" />
        <result column="interceptTime" property="interceptTime" />
        <result column="isLogin" property="isLogin" />

        <result column="postMax" property="postMax" />
        <result column="forumAudit" property="forumAudit" />
        <result column="spaceAudit" property="spaceAudit" />
        <result column="uploadType" property="uploadType" />
        <result column="banRobots" property="banRobots" />
        <result column="adsGiftNum" property="adsGiftNum" />
        <result column="adsGiftAward" property="adsGiftAward" />
        <result column="verifyLevel" property="verifyLevel" />

        <result column="rebateLevel" property="rebateLevel" />
        <result column="rebateNum" property="rebateNum" />
        <result column="rebateProportion" property="rebateProportion" />

        <result column="uploadPicMax" property="uploadPicMax" />
        <result column="uploadMediaMax" property="uploadMediaMax" />
        <result column="uploadFilesMax" property="uploadFilesMax" />

        <result column="identifyiLv" property="identifyiLv" />
        <result column="identifyiIdcardHost" property="identifyiIdcardHost" />
        <result column="identifyiIdcardPath" property="identifyiIdcardPath" />
        <result column="identifyiIdcardAppcode" property="identifyiIdcardAppcode" />
        <result column="identifyiCompanyHost" property="identifyiCompanyHost" />
        <result column="identifyiCompanyPath" property="identifyiCompanyPath" />
        <result column="identifyiCompanyAppcode" property="identifyiCompanyAppcode" />

        <result column="clockPoints" property="clockPoints" />

        <result column="cmsSecretId" property="cmsSecretId" />
        <result column="cmsSecretKey" property="cmsSecretKey" />
        <result column="cmsRegion" property="cmsRegion" />
        <result column="cmsSwitch" property="cmsSwitch" />
        <result column="localPath" property="localPath" />

        <result column="adsVideoType" property="adsVideoType" />
        <result column="adsSecuritykey" property="adsSecuritykey" />
        <result column="forumReplyAudit" property="forumReplyAudit" />
        <result column="banIP" property="banIP" />
        <result column="smsType" property="smsType" />

        <result column="smsbaoUsername" property="smsbaoUsername" />
        <result column="smsbaoApikey" property="smsbaoApikey" />
        <result column="smsbaoTemplate" property="smsbaoTemplate" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `webinfoTitle`,
        `webinfoUrl`,
        `webinfoUploadUrl`,
        `webinfoAvatar`,
        `pexelsKey`,
        `scale`,
        `clock`,
        `vipPrice`,
        `vipDay`,
        `vipDiscount`,
        `isEmail`,
        `isInvite`,
        `cosAccessKey`,
        `cosSecretKey`,
        `cosBucket`,
        `cosBucketName`,
        `cosPath`,
        `cosPrefix`,
        `aliyunEndpoint`,
        `aliyunAccessKeyId`,
        `aliyunAccessKeySecret`,
        `aliyunAucketName`,
        `aliyunUrlPrefix`,
        `aliyunFilePrefix`,
        `ftpHost`,
        `ftpPort`,
        `ftpUsername`,
        `ftpPassword`,
        `ftpBasePath`,
        `alipayAppId`,
        `alipayPrivateKey`,
        `alipayPublicKey`,
        `alipayNotifyUrl`,

        `wxAppId`,
        `wxAppSecret`,
        `appletsAppid`,
        `appletsSecret`,
        `qqAppletsAppid`,
        `qqAppletsSecret`,
        `wxpayAppId`,
        `wxpayMchId`,
        `wxpayKey`,
        `wxpayNotifyUrl`,
        `mchSerialNo`,
        `mchApiV3Key`,

        `auditlevel`,
        `forbidden`,
        `fields`,

        `pushAdsPrice`,
        `pushAdsNum`,
        `bannerAdsPrice`,
        `bannerAdsNum`,
        `startAdsPrice`,
        `startAdsNum`,

        `epayUrl`,
        `epayPid`,
        `epayKey`,
        `epayNotifyUrl`,
        `cloudUid`,
        `cloudUrl`,

        `pushAppId`,
        `pushAppKey`,
        `pushMasterSecret`,
        `disableCode`,
        `allowDelete`,
        `contentAuditlevel`,
        `isPush`,
        `uploadLevel`,
        `clockExp`,
        `reviewExp`,
        `postExp`,
        `violationExp`,
        `deleteExp`,
        `spaceMinExp`,
        `chatMinExp`,
        `qiniuDomain`,
        `qiniuAccessKey`,
        `qiniuSecretKey`,
        `qiniuBucketName`,
        `codeAccessKeyId`,
        `codeAccessKeySecret`,
        `codeEndpoint`,
        `codeTemplate`,
        `codeSignName`,
        `isPhone`,
        `silenceTime`,
        `interceptTime`,
        `isLogin`,
        `postMax`,
        `forumAudit`,
        `spaceAudit`,
        `uploadType`,
        `banRobots`,
        `adsGiftNum`,
        `adsGiftAward`,
        `verifyLevel`,
        `rebateLevel`,
        `rebateNum`,
        `rebateProportion`,

        `uploadPicMax`,
        `uploadMediaMax`,
        `uploadFilesMax`,

        `identifyiLv`,
        `identifyiIdcardHost`,
        `identifyiIdcardPath`,
        `identifyiIdcardAppcode`,
        `identifyiCompanyHost`,
        `identifyiCompanyPath`,
        `identifyiCompanyAppcode`,
        `clockPoints`,
        `identifylvPost`,
        `identifysmPost`,
        `cmsSecretId`,
        `cmsSecretKey`,
        `cmsRegion`,
        `cmsSwitch`,
        `localPath`,
        `adsVideoType`,
        `adsSecuritykey`,
        `forumReplyAudit`,
        `banIP`,
        `smsType`,
        `smsbaoUsername`,
        `smsbaoApikey`,
        `smsbaoTemplate`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoApiconfig">
        INSERT INTO ${prefix}_apiconfig
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != webinfoTitle'>
                `webinfoTitle`,
            </if>
            <if test ='null != webinfoUrl'>
                `webinfoUrl`,
            </if>
            <if test ='null != webinfoUploadUrl'>
                `webinfoUploadUrl`,
            </if>
            <if test ='null != webinfoAvatar'>
                `webinfoAvatar`,
            </if>
            <if test ='null != pexelsKey'>
                `pexelsKey`,
            </if>
            <if test ='null != scale'>
                `scale`,
            </if>
            <if test ='null != clock'>
                `clock`,
            </if>
            <if test ='null != vipPrice'>
                `vipPrice`,
            </if>
            <if test ='null != vipDay'>
                `vipDay`,
            </if>
            <if test ='null != vipDiscount'>
                `vipDiscount`,
            </if>
            <if test ='null != isEmail'>
                `isEmail`,
            </if>
            <if test ='null != isInvite'>
                `isInvite`,
            </if>
            <if test ='null != cosAccessKey'>
                `cosAccessKey`,
            </if>
            <if test ='null != cosSecretKey'>
                `cosSecretKey`,
            </if>
            <if test ='null != cosBucket'>
                `cosBucket`,
            </if>
            <if test ='null != cosBucketName'>
                `cosBucketName`,
            </if>
            <if test ='null != cosPath'>
                `cosPath`,
            </if>
            <if test ='null != cosPrefix'>
                `cosPrefix`,
            </if>
            <if test ='null != aliyunEndpoint'>
                `aliyunEndpoint`,
            </if>
            <if test ='null != aliyunAccessKeyId'>
                `aliyunAccessKeyId`,
            </if>
            <if test ='null != aliyunAccessKeySecret'>
                `aliyunAccessKeySecret`,
            </if>
            <if test ='null != aliyunAucketName'>
                `aliyunAucketName`,
            </if>
            <if test ='null != aliyunUrlPrefix'>
                `aliyunUrlPrefix`,
            </if>
            <if test ='null != aliyunFilePrefix'>
                `aliyunFilePrefix`,
            </if>
            <if test ='null != ftpHost'>
                `ftpHost`,
            </if>
            <if test ='null != ftpPort'>
                `ftpPort`,
            </if>
            <if test ='null != ftpUsername'>
                `ftpUsername`,
            </if>
            <if test ='null != ftpPassword'>
                `ftpPassword`,
            </if>
            <if test ='null != ftpBasePath'>
                `ftpBasePath`,
            </if>
            <if test ='null != alipayAppId'>
                `alipayAppId`,
            </if>
            <if test ='null != alipayPrivateKey'>
                `alipayPrivateKey`,
            </if>
            <if test ='null != alipayPublicKey'>
                `alipayPublicKey`,
            </if>
            <if test ='null != alipayNotifyUrl'>
                `alipayNotifyUrl`,
            </if>
            <if test ='null != wxAppId'>
                `wxAppId`,
            </if>
            <if test ='null != wxAppSecret'>
                `wxAppSecret`,
            </if>
            <if test ='null != appletsAppid'>
                `appletsAppid`,
            </if>
            <if test ='null != appletsSecret'>
                `appletsSecret`,
            </if>
            <if test ='null != qqAppletsAppid'>
                `qqAppletsAppid`,
            </if>
            <if test ='null != qqAppletsSecret'>
                `qqAppletsSecret`,
            </if>
            <if test ='null != wxpayAppId'>
                `wxpayAppId`,
            </if>
            <if test ='null != wxpayMchId'>
                `wxpayMchId`,
            </if>
            <if test ='null != wxpayKey'>
                `wxpayKey`,
            </if>
            <if test ='null != wxpayNotifyUrl'>
                `wxpayNotifyUrl`,
            </if>
            <if test ='null != mchSerialNo'>
                `mchSerialNo`,
            </if>
            <if test ='null != mchApiV3Key'>
                `mchApiV3Key`,
            </if>
            <if test ='null != auditlevel'>
                `auditlevel`,
            </if>
            <if test ='null != forbidden'>
                `forbidden`,
            </if>
            <if test ='null != fields'>
                `fields`,
            </if>

            <if test ='null != pushAdsPrice'>
                `pushAdsPrice`,
            </if>
            <if test ='null != pushAdsNum'>
                `pushAdsNum`,
            </if>
            <if test ='null != bannerAdsPrice'>
                `bannerAdsPrice`,
            </if>
            <if test ='null != bannerAdsNum'>
                `bannerAdsNum`,
            </if>
            <if test ='null != startAdsPrice'>
                `startAdsPrice`,
            </if>
            <if test ='null != startAdsNum'>
                `startAdsNum`,
            </if>
            <if test ='null != epayUrl'>
                `epayUrl`,
            </if>
            <if test ='null != epayPid'>
                `epayPid`,
            </if>
            <if test ='null != epayKey'>
                `epayKey`,
            </if>
            <if test ='null != epayNotifyUrl'>
                `epayNotifyUrl`,
            </if>
            <if test ='null != cloudUid'>
                `cloudUid`,
            </if>
            <if test ='null != cloudUrl'>
                `cloudUrl`,
            </if>
            <if test ='null != pushAppId'>
                `pushAppId`,
            </if>
            <if test ='null != pushAppKey'>
                `pushAppKey`,
            </if>
            <if test ='null != pushMasterSecret'>
                `pushMasterSecret`,
            </if>
            <if test ='null != disableCode'>
                `disableCode`,
            </if>
            <if test ='null != allowDelete'>
                `allowDelete`,
            </if>
            <if test ='null != contentAuditlevel'>
                `contentAuditlevel`,
            </if>
            <if test ='null != isPush'>
                `isPush`,
            </if>
            <if test ='null != uploadLevel'>
                `uploadLevel`,
            </if>
            <if test ='null != clockExp'>
                `clockExp`,
            </if>
            <if test ='null != reviewExp'>
                `reviewExp`,
            </if>
            <if test ='null != postExp'>
                `postExp`,
            </if>
            <if test ='null != violationExp'>
                `violationExp`,
            </if>
            <if test ='null != deleteExp'>
                `deleteExp`,
            </if>
            <if test ='null != spaceMinExp'>
                `spaceMinExp`,
            </if>
            <if test ='null != chatMinExp'>
                `chatMinExp`,
            </if>
            <if test ='null != qiniuDomain'>
                `qiniuDomain`,
            </if>
            <if test ='null != qiniuAccessKey'>
                `qiniuAccessKey`,
            </if>
            <if test ='null != qiniuSecretKey'>
                `qiniuSecretKey`,
            </if>
            <if test ='null != qiniuBucketName'>
                `qiniuBucketName`,
            </if>
            <if test ='null != codeAccessKeyId'>
                `codeAccessKeyId`,
            </if>
            <if test ='null != codeAccessKeySecret'>
                `codeAccessKeySecret`,
            </if>
            <if test ='null != codeEndpoint'>
                `codeEndpoint`,
            </if>
            <if test ='null != codeTemplate'>
                `codeTemplate`,
            </if>
            <if test ='null != codeSignName'>
                `codeSignName`,
            </if>
            <if test ='null != isPhone'>
                `isPhone`,
            </if>
            <if test ='null != silenceTime'>
                `silenceTime`,
            </if>
            <if test ='null != interceptTime'>
                `interceptTime`,
            </if>
            <if test ='null != isLogin'>
                `isLogin`,
            </if>
            <if test ='null != postMax'>
                `postMax`,
            </if>
            <if test ='null != forumAudit'>
                `forumAudit`,
            </if>
            <if test ='null != spaceAudit'>
                `spaceAudit`,
            </if>
            <if test ='null != uploadType'>
                `uploadType`,
            </if>
            <if test ='null != banRobots'>
                `banRobots`,
            </if>
            <if test ='null != adsGiftNum'>
                `adsGiftNum`,
            </if>
            <if test ='null != adsGiftAward'>
                `adsGiftAward`,
            </if>
            <if test ='null != verifyLevel'>
                `verifyLevel`,
            </if>
            <if test ='null != rebateLevel'>
                `rebateLevel`,
            </if>
            <if test ='null != rebateNum'>
                `rebateNum`,
            </if>
            <if test ='null != rebateProportion'>
                `rebateProportion`,
            </if>
            <if test ='null != uploadPicMax'>
                `uploadPicMax`,
            </if>
            <if test ='null != uploadMediaMax'>
                `uploadMediaMax`,
            </if>
            <if test ='null != uploadFilesMax'>
                `uploadFilesMax`,
            </if>
            <if test ='null != identifyiLv'>
                `identifyiLv`,
            </if>
            <if test ='null != identifyiIdcardHost'>
                `identifyiIdcardHost`,
            </if>
            <if test ='null != identifyiIdcardPath'>
                `identifyiIdcardPath`,
            </if>
            <if test ='null != identifyiIdcardAppcode'>
                `identifyiIdcardAppcode`,
            </if>
            <if test ='null != identifyiCompanyHost'>
                `identifyiCompanyHost`,
            </if>
            <if test ='null != identifyiCompanyPath'>
                `identifyiCompanyPath`,
            </if>
            <if test ='null != identifyiCompanyAppcode'>
                `identifyiCompanyAppcode`,
            </if>
            <if test ='null != clockPoints'>
                `clockPoints`,
            </if>
            <if test ='null != identifylvPost'>
                `identifylvPost`,
            </if>
            <if test ='null != identifysmPost'>
                `identifysmPost`,
            </if>
            <if test ='null != cmsSecretId'>
                `cmsSecretId`,
            </if>
            <if test ='null != cmsSecretKey'>
                `cmsSecretKey`,
            </if>
            <if test ='null != cmsRegion'>
                `cmsRegion`,
            </if>
            <if test ='null != cmsSwitch'>
                `cmsSwitch`,
            </if>
            <if test ='null != localPath'>
                `localPath`,
            </if>
            <if test ='null != adsVideoType'>
                `adsVideoType`,
            </if>
            <if test ='null != adsSecuritykey'>
                `adsSecuritykey`,
            </if>
            <if test ='null != forumReplyAudit'>
                `forumReplyAudit`
            </if>
            <if test ='null != banIP'>
                `banIP`,
            </if>
            <if test ='null != smsType'>
                `smsType`,
            </if>
            <if test ='null != smsbaoUsername'>
                `smsbaoUsername`,
            </if>
            <if test ='null != smsbaoApikey'>
                `smsbaoApikey`,
            </if>
            <if test ='null != smsbaoTemplate'>
                `smsbaoTemplate`
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != webinfoTitle'>
                #{webinfoTitle},
            </if>
            <if test ='null != webinfoUrl'>
                #{webinfoUrl},
            </if>
            <if test ='null != webinfoUploadUrl'>
                #{webinfoUploadUrl},
            </if>
            <if test ='null != webinfoAvatar'>
                #{webinfoAvatar},
            </if>
            <if test ='null != pexelsKey'>
                #{pexelsKey},
            </if>
            <if test ='null != scale'>
                #{scale},
            </if>
            <if test ='null != clock'>
                #{clock},
            </if>
            <if test ='null != vipPrice'>
                #{vipPrice},
            </if>
            <if test ='null != vipDay'>
                #{vipDay},
            </if>
            <if test ='null != vipDiscount'>
                #{vipDiscount},
            </if>
            <if test ='null != isEmail'>
                #{isEmail},
            </if>
            <if test ='null != isInvite'>
                #{isInvite},
            </if>
            <if test ='null != cosAccessKey'>
                #{cosAccessKey},
            </if>
            <if test ='null != cosSecretKey'>
                #{cosSecretKey},
            </if>
            <if test ='null != cosBucket'>
                #{cosBucket},
            </if>
            <if test ='null != cosBucketName'>
                #{cosBucketName},
            </if>
            <if test ='null != cosPath'>
                #{cosPath},
            </if>
            <if test ='null != cosPrefix'>
                #{cosPrefix},
            </if>
            <if test ='null != aliyunEndpoint'>
                #{aliyunEndpoint},
            </if>
            <if test ='null != aliyunAccessKeyId'>
                #{aliyunAccessKeyId},
            </if>
            <if test ='null != aliyunAccessKeySecret'>
                #{aliyunAccessKeySecret},
            </if>
            <if test ='null != aliyunAucketName'>
                #{aliyunAucketName},
            </if>
            <if test ='null != aliyunUrlPrefix'>
                #{aliyunUrlPrefix},
            </if>
            <if test ='null != aliyunFilePrefix'>
                #{aliyunFilePrefix},
            </if>
            <if test ='null != ftpHost'>
                #{ftpHost},
            </if>
            <if test ='null != ftpPort'>
                #{ftpPort},
            </if>
            <if test ='null != ftpUsername'>
                #{ftpUsername},
            </if>
            <if test ='null != ftpPassword'>
                #{ftpPassword},
            </if>
            <if test ='null != ftpBasePath'>
                #{ftpBasePath},
            </if>
            <if test ='null != alipayAppId'>
                #{alipayAppId},
            </if>
            <if test ='null != alipayPrivateKey'>
                #{alipayPrivateKey},
            </if>
            <if test ='null != alipayPublicKey'>
                #{alipayPublicKey},
            </if>
            <if test ='null != alipayNotifyUrl'>
                #{alipayNotifyUrl},
            </if>

            <if test ='null != wxAppId'>
                #{wxAppId},
            </if>
            <if test ='null != wxAppSecret'>
                #{wxAppSecret},
            </if>
            <if test ='null != appletsAppid'>
                #{appletsAppid},
            </if>
            <if test ='null != appletsSecret'>
                #{appletsSecret},
            </if>
            <if test ='null != qqAppletsAppid'>
                #{qqAppletsAppid},
            </if>
            <if test ='null != qqAppletsSecret'>
                #{qqAppletsSecret},
            </if>
            <if test ='null != wxpayAppId'>
                #{wxpayAppId},
            </if>
            <if test ='null != wxpayMchId'>
                #{wxpayMchId},
            </if>
            <if test ='null != wxpayKey'>
                #{wxpayKey},
            </if>
            <if test ='null != wxpayNotifyUrl'>
                #{wxpayNotifyUrl},
            </if>
            <if test ='null != mchSerialNo'>
                #{mchSerialNo},
            </if>
            <if test ='null != mchApiV3Key'>
                #{mchApiV3Key},
            </if>
            <if test ='null != auditlevel'>
                #{auditlevel},
            </if>
            <if test ='null != forbidden'>
                #{forbidden},
            </if>
            <if test ='null != fields'>
                #{fields},
            </if>
            <if test ='null != pushAdsPrice'>
                #{pushAdsPrice},
            </if>
            <if test ='null != pushAdsNum'>
                #{pushAdsNum},
            </if>
            <if test ='null != bannerAdsPrice'>
                #{bannerAdsPrice},
            </if>
            <if test ='null != bannerAdsNum'>
                #{bannerAdsNum},
            </if>
            <if test ='null != startAdsPrice'>
                #{startAdsPrice},
            </if>
            <if test ='null != startAdsNum'>
                #{startAdsNum},
            </if>
            <if test ='null != epayUrl'>
                #{epayUrl},
            </if>
            <if test ='null != epayPid'>
                #{epayPid},
            </if>
            <if test ='null != epayKey'>
                #{epayKey},
            </if>
            <if test ='null != epayNotifyUrl'>
                #{epayNotifyUrl},
            </if>
            <if test ='null != cloudUid'>
                #{cloudUid},
            </if>
            <if test ='null != cloudUrl'>
                #{cloudUrl},
            </if>
            <if test ='null != pushAppId'>
                #{pushAppId},
            </if>
            <if test ='null != pushAppKey'>
                #{pushAppKey},
            </if>
            <if test ='null != pushMasterSecret'>
                #{pushMasterSecret},
            </if>
            <if test ='null != disableCode'>
                #{disableCode},
            </if>
            <if test ='null != allowDelete'>
                #{allowDelete},
            </if>
            <if test ='null != contentAuditlevel'>
                #{contentAuditlevel},
            </if>
            <if test ='null != isPush'>
                #{isPush},
            </if>
            <if test ='null != uploadLevel'>
                #{uploadLevel},
            </if>
            <if test ='null != clockExp'>
                #{clockExp},
            </if>
            <if test ='null != reviewExp'>
                #{reviewExp},
            </if>
            <if test ='null != postExp'>
                #{postExp},
            </if>
            <if test ='null != violationExp'>
                #{violationExp},
            </if>
            <if test ='null != deleteExp'>
                #{deleteExp},
            </if>
            <if test ='null != spaceMinExp'>
                #{spaceMinExp},
            </if>
            <if test ='null != chatMinExp'>
                #{chatMinExp},
            </if>
            <if test ='null != qiniuDomain'>
                #{qiniuDomain},
            </if>
            <if test ='null != qiniuAccessKey'>
                #{qiniuAccessKey},
            </if>
            <if test ='null != qiniuSecretKey'>
                #{qiniuSecretKey},
            </if>
            <if test ='null != qiniuBucketName'>
                #{qiniuBucketName},
            </if>
            <if test ='null != codeAccessKeyId'>
                #{codeAccessKeyId},
            </if>
            <if test ='null != codeAccessKeySecret'>
                #{codeAccessKeySecret},
            </if>
            <if test ='null != codeEndpoint'>
                #{codeEndpoint},
            </if>
            <if test ='null != codeTemplate'>
                #{codeTemplate},
            </if>
            <if test ='null != codeSignName'>
                #{codeSignName},
            </if>
            <if test ='null != isPhone'>
                #{isPhone},
            </if>
            <if test ='null != silenceTime'>
                #{silenceTime},
            </if>
            <if test ='null != interceptTime'>
                #{interceptTime},
            </if>
            <if test ='null != isLogin'>
                #{isLogin},
            </if>
            <if test ='null != postMax'>
                #{postMax},
            </if>
            <if test ='null != forumAudit'>
                #{forumAudit},
            </if>
            <if test ='null != spaceAudit'>
                #{spaceAudit},
            </if>
            <if test ='null != uploadType'>
                #{uploadType},
            </if>
            <if test ='null != banRobots'>
                #{banRobots},
            </if>
            <if test ='null != adsGiftNum'>
                #{adsGiftNum},
            </if>
            <if test ='null != adsGiftAward'>
                #{adsGiftAward},
            </if>
            <if test ='null != verifyLevel'>
                #{verifyLevel},
            </if>
            <if test ='null != rebateLevel'>
                #{rebateLevel},
            </if>
            <if test ='null != rebateNum'>
                #{rebateNum},
            </if>
            <if test ='null != rebateProportion'>
                #{rebateProportion},
            </if>
            <if test ='null != uploadPicMax'>
                #{uploadPicMax},
            </if>
            <if test ='null != uploadMediaMax'>
                #{uploadMediaMax},
            </if>
            <if test ='null != uploadFilesMax'>
                #{uploadFilesMax},
            </if>
            <if test ='null != identifyiLv'>
                #{identifyiLv},
            </if>
            <if test ='null != identifyiIdcardHost'>
                #{identifyiIdcardHost},
            </if>
            <if test ='null != identifyiIdcardPath'>
                #{identifyiIdcardPath},
            </if>
            <if test ='null != identifyiIdcardAppcode'>
                #{identifyiIdcardAppcode},
            </if>
            <if test ='null != identifyiCompanyHost'>
                #{identifyiCompanyHost},
            </if>
            <if test ='null != identifyiCompanyPath'>
                #{identifyiCompanyPath},
            </if>
            <if test ='null != identifyiCompanyAppcode'>
                #{identifyiCompanyAppcode},
            </if>
            <if test ='null != clockPoints'>
                #{clockPoints},
            </if>
            <if test ='null != identifylvPost'>
                #{identifylvPost},
            </if>
            <if test ='null != identifysmPost'>
                #{identifysmPost},
            </if>
            <if test ='null != cmsSecretId'>
                #{cmsSecretId},
            </if>
            <if test ='null != cmsSecretKey'>
                #{cmsSecretKey},
            </if>
            <if test ='null != cmsRegion'>
                #{cmsRegion},
            </if>
            <if test ='null != cmsSwitch'>
                #{cmsSwitch},
            </if>
            <if test ='null != localPath'>
                #{localPath},
            </if>
            <if test ='null != adsVideoType'>
                #{adsVideoType},
            </if>
            <if test ='null != adsSecuritykey'>
                #{adsSecuritykey},
            </if>
            <if test ='null != forumReplyAudit'>
                #{forumReplyAudit},
            </if>
            <if test ='null != banIP'>
                #{banIP},
            </if>
            <if test ='null != smsType'>
                #{smsType},
            </if>
            <if test ='null != smsbaoUsername'>
                #{smsbaoUsername},
            </if>
            <if test ='null != smsbaoApikey'>
                #{smsbaoApikey},
            </if>
            <if test ='null != smsbaoTemplate'>
                #{smsbaoTemplate}
            </if>

        </trim>
    </insert>


    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoApiconfig">
        UPDATE ${prefix}_apiconfig
        <set>
            <if test ='null != webinfoTitle'>`webinfoTitle` = #{webinfoTitle},</if>
            <if test ='null != webinfoUrl'>`webinfoUrl` = #{webinfoUrl},</if>
            <if test ='null != webinfoUploadUrl'>`webinfoUploadUrl` = #{webinfoUploadUrl},</if>
            <if test ='null != webinfoAvatar'>`webinfoAvatar` = #{webinfoAvatar},</if>
            <if test ='null != pexelsKey'>`pexelsKey` = #{pexelsKey},</if>
            <if test ='null != scale'>`scale` = #{scale},</if>
            <if test ='null != clock'>`clock` = #{clock},</if>
            <if test ='null != vipPrice'>`vipPrice` = #{vipPrice},</if>
            <if test ='null != vipDay'>`vipDay` = #{vipDay},</if>
            <if test ='null != vipDiscount'>`vipDiscount` = #{vipDiscount},</if>
            <if test ='null != isEmail'>`isEmail` = #{isEmail},</if>
            <if test ='null != isInvite'>`isInvite` = #{isInvite},</if>
            <if test ='null != cosAccessKey'>`cosAccessKey` = #{cosAccessKey},</if>
            <if test ='null != cosSecretKey'>`cosSecretKey` = #{cosSecretKey},</if>
            <if test ='null != cosBucket'>`cosBucket` = #{cosBucket},</if>
            <if test ='null != cosBucketName'>`cosBucketName` = #{cosBucketName},</if>
            <if test ='null != cosPath'>`cosPath` = #{cosPath},</if>
            <if test ='null != cosPrefix'>`cosPrefix` = #{cosPrefix},</if>
            <if test ='null != aliyunEndpoint'>`aliyunEndpoint` = #{aliyunEndpoint},</if>
            <if test ='null != aliyunAccessKeyId'>`aliyunAccessKeyId` = #{aliyunAccessKeyId},</if>
            <if test ='null != aliyunAccessKeySecret'>`aliyunAccessKeySecret` = #{aliyunAccessKeySecret},</if>
            <if test ='null != aliyunAucketName'>`aliyunAucketName` = #{aliyunAucketName},</if>
            <if test ='null != aliyunUrlPrefix'>`aliyunUrlPrefix` = #{aliyunUrlPrefix},</if>
            <if test ='null != aliyunFilePrefix'>`aliyunFilePrefix` = #{aliyunFilePrefix},</if>
            <if test ='null != ftpHost'>`ftpHost` = #{ftpHost},</if>
            <if test ='null != ftpPort'>`ftpPort` = #{ftpPort},</if>
            <if test ='null != ftpUsername'>`ftpUsername` = #{ftpUsername},</if>
            <if test ='null != ftpPassword'>`ftpPassword` = #{ftpPassword},</if>
            <if test ='null != ftpBasePath'>`ftpBasePath` = #{ftpBasePath},</if>
            <if test ='null != alipayAppId'>`alipayAppId` = #{alipayAppId},</if>
            <if test ='null != alipayPrivateKey'>`alipayPrivateKey` = #{alipayPrivateKey},</if>
            <if test ='null != alipayPublicKey'>`alipayPublicKey` = #{alipayPublicKey},</if>
            <if test ='null != alipayNotifyUrl'>`alipayNotifyUrl` = #{alipayNotifyUrl},</if>

            <if test ='null != wxAppId'>`wxAppId` = #{wxAppId},</if>
            <if test ='null != wxAppSecret'>`wxAppSecret` = #{wxAppSecret},</if>
            <if test ='null != appletsAppid'>`appletsAppid` = #{appletsAppid},</if>
            <if test ='null != appletsSecret'>`appletsSecret` = #{appletsSecret},</if>
            <if test ='null != qqAppletsAppid'>`qqAppletsAppid` = #{qqAppletsAppid},</if>
            <if test ='null != qqAppletsSecret'>`qqAppletsSecret` = #{qqAppletsSecret},</if>
            <if test ='null != wxpayAppId'>`wxpayAppId` = #{wxpayAppId},</if>
            <if test ='null != wxpayMchId'>`wxpayMchId` = #{wxpayMchId},</if>
            <if test ='null != wxpayKey'>`wxpayKey` = #{wxpayKey},</if>
            <if test ='null != wxpayNotifyUrl'>`wxpayNotifyUrl` = #{wxpayNotifyUrl},</if>
            <if test ='null != mchSerialNo'>`mchSerialNo` = #{mchSerialNo},</if>
            <if test ='null != mchApiV3Key'>`mchApiV3Key` = #{mchApiV3Key},</if>
            <if test ='null != auditlevel'>`auditlevel` = #{auditlevel},</if>
            <if test ='null != forbidden'>`forbidden` = #{forbidden},</if>
            <if test ='null != fields'>`fields` = #{fields},</if>
            <if test ='null != pushAdsPrice'>`pushAdsPrice` = #{pushAdsPrice},</if>
            <if test ='null != pushAdsNum'>`pushAdsNum` = #{pushAdsNum},</if>
            <if test ='null != bannerAdsPrice'>`bannerAdsPrice` = #{bannerAdsPrice},</if>
            <if test ='null != bannerAdsNum'>`bannerAdsNum` = #{bannerAdsNum},</if>
            <if test ='null != startAdsPrice'>`startAdsPrice` = #{startAdsPrice},</if>
            <if test ='null != startAdsNum'>`startAdsNum` = #{startAdsNum},</if>

            <if test ='null != epayUrl'>`epayUrl` = #{epayUrl},</if>
            <if test ='null != epayPid'>`epayPid` = #{epayPid},</if>
            <if test ='null != epayKey'>`epayKey` = #{epayKey},</if>
            <if test ='null != epayNotifyUrl'>`epayNotifyUrl` = #{epayNotifyUrl},</if>
            <if test ='null != cloudUid'>`cloudUid` = #{cloudUid},</if>
            <if test ='null != cloudUrl'>`cloudUrl` = #{cloudUrl},</if>

            <if test ='null != pushAppId'>`pushAppId` = #{pushAppId},</if>
            <if test ='null != pushAppKey'>`pushAppKey` = #{pushAppKey},</if>
            <if test ='null != pushMasterSecret'>`pushMasterSecret` = #{pushMasterSecret},</if>
            <if test ='null != disableCode'>`disableCode` = #{disableCode},</if>
            <if test ='null != allowDelete'>`allowDelete` = #{allowDelete},</if>
            <if test ='null != contentAuditlevel'>`contentAuditlevel` = #{contentAuditlevel},</if>
            <if test ='null != isPush'>`isPush` = #{isPush},</if>
            <if test ='null != uploadLevel'>`uploadLevel` = #{uploadLevel},</if>

            <if test ='null != clockExp'>`clockExp` = #{clockExp},</if>
            <if test ='null != reviewExp'>`reviewExp` = #{reviewExp},</if>
            <if test ='null != postExp'>`postExp` = #{postExp},</if>
            <if test ='null != violationExp'>`violationExp` = #{violationExp},</if>
            <if test ='null != deleteExp'>`deleteExp` = #{deleteExp},</if>
            <if test ='null != spaceMinExp'>`spaceMinExp` = #{spaceMinExp},</if>
            <if test ='null != chatMinExp'>`chatMinExp` = #{chatMinExp},</if>
            <if test ='null != qiniuDomain'>`qiniuDomain` = #{qiniuDomain},</if>
            <if test ='null != qiniuAccessKey'>`qiniuAccessKey` = #{qiniuAccessKey},</if>
            <if test ='null != qiniuSecretKey'>`qiniuSecretKey` = #{qiniuSecretKey},</if>
            <if test ='null != qiniuBucketName'>`qiniuBucketName` = #{qiniuBucketName},</if>
            <if test ='null != codeAccessKeyId'>`codeAccessKeyId` = #{codeAccessKeyId},</if>
            <if test ='null != codeAccessKeySecret'>`codeAccessKeySecret` = #{codeAccessKeySecret},</if>
            <if test ='null != codeEndpoint'>`codeEndpoint` = #{codeEndpoint},</if>
            <if test ='null != codeTemplate'>`codeTemplate` = #{codeTemplate},</if>
            <if test ='null != codeSignName'>`codeSignName` = #{codeSignName},</if>
            <if test ='null != isPhone'>`isPhone` = #{isPhone},</if>
            <if test ='null != silenceTime'>`silenceTime` = #{silenceTime},</if>
            <if test ='null != interceptTime'>`interceptTime` = #{interceptTime},</if>
            <if test ='null != isLogin'>`isLogin` = #{isLogin},</if>
            <if test ='null != postMax'>`postMax` = #{postMax},</if>
            <if test ='null != forumAudit'>`forumAudit` = #{forumAudit},</if>
            <if test ='null != spaceAudit'>`spaceAudit` = #{spaceAudit},</if>
            <if test ='null != uploadType'>`uploadType` = #{uploadType},</if>
            <if test ='null != banRobots'>`banRobots` = #{banRobots},</if>
            <if test ='null != adsGiftNum'>`adsGiftNum` = #{adsGiftNum},</if>
            <if test ='null != adsGiftAward'>`adsGiftAward` = #{adsGiftAward},</if>
            <if test ='null != verifyLevel'>`verifyLevel` = #{verifyLevel},</if>
            <if test ='null != rebateLevel'>`rebateLevel` = #{rebateLevel},</if>
            <if test ='null != rebateNum'>`rebateNum` = #{rebateNum},</if>
            <if test ='null != rebateProportion'>`rebateProportion` = #{rebateProportion},</if>

            <if test ='null != uploadPicMax'>`uploadPicMax` = #{uploadPicMax},</if>
            <if test ='null != uploadMediaMax'>`uploadMediaMax` = #{uploadMediaMax},</if>
            <if test ='null != uploadFilesMax'>`uploadFilesMax` = #{uploadFilesMax},</if>

            <if test ='null != identifyiLv'>`identifyiLv` = #{identifyiLv},</if>
            <if test ='null != identifyiIdcardHost'>`identifyiIdcardHost` = #{identifyiIdcardHost},</if>
            <if test ='null != identifyiIdcardPath'>`identifyiIdcardPath` = #{identifyiIdcardPath},</if>
            <if test ='null != identifyiIdcardAppcode'>`identifyiIdcardAppcode` = #{identifyiIdcardAppcode},</if>
            <if test ='null != identifyiCompanyHost'>`identifyiCompanyHost` = #{identifyiCompanyHost},</if>
            <if test ='null != identifyiCompanyPath'>`identifyiCompanyPath` = #{identifyiCompanyPath},</if>
            <if test ='null != identifyiCompanyAppcode'>`identifyiCompanyAppcode` = #{identifyiCompanyAppcode},</if>
            <if test ='null != clockPoints'>`clockPoints` = #{clockPoints},</if>
            <if test ='null != identifylvPost'>`identifylvPost` = #{identifylvPost},</if>
            <if test ='null != identifysmPost'>`identifysmPost` = #{identifysmPost},</if>
            <if test ='null != cmsSecretId'>`cmsSecretId` = #{cmsSecretId},</if>
            <if test ='null != cmsSecretKey'>`cmsSecretKey` = #{cmsSecretKey},</if>
            <if test ='null != cmsRegion'>`cmsRegion` = #{cmsRegion},</if>
            <if test ='null != cmsSwitch'>`cmsSwitch` = #{cmsSwitch},</if>
            <if test ='null != localPath'>`localPath` = #{localPath},</if>
            <if test ='null != adsVideoType'>`adsVideoType` = #{adsVideoType},</if>
            <if test ='null != adsSecuritykey'>`adsSecuritykey` = #{adsSecuritykey},</if>
            <if test ='null != forumReplyAudit'>`forumReplyAudit` = #{forumReplyAudit},</if>
            <if test ='null != banIP'>`banIP` = #{banIP},</if>
            <if test ='null != smsType'>`smsType` = #{smsType},</if>
            <if test ='null != smsbaoUsername'>`smsbaoUsername` = #{smsbaoUsername},</if>
            <if test ='null != smsbaoApikey'>`smsbaoApikey` = #{smsbaoApikey},</if>
            <if test ='null != smsbaoTemplate'>`smsbaoTemplate` = #{smsbaoTemplate}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_apiconfig
        WHERE `id` = #{key}
    </select>


</mapper>