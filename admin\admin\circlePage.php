<?php
session_start();
?>
<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_pages";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">圈子页设置</h4>

                <form class="needs-validation" action="circlePagePost.php" method="post" onsubmit="return check()"
                      novalidate>
                   <div class="form-group col-sm-4" id="swiperType">
                        <label for="validationCustom01">轮播图风格</label>
                        <select class="form-control" id="example-select" name="swiperType">
                            <?php
                            if ($row['swiperType']==2) {
                                echo '<option value="2" selected>经典</option>
                                      <option value="1">唯美</option>';
                             }else{
                                 echo '<option value="2">经典</option>
                                  <option value="1" selected>唯美</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    <div class="form-group col-sm-4" id="swiperStyle2">
                        <label for="validationCustom01">轮播图样式（仅在唯美风格生效）</label>
                        <select class="form-control" id="example-select" name="swiperStyle2">
                            <?php
                            if ($row['swiperStyle2']=='true') {
                                echo '<option value="true" selected>多图</option>
                                      <option value="false">单图</option>';
                             }else{
                                 echo '<option value="true">多图</option>
                                  <option value="false" selected>单图</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler4(obj) {
                            var input = document.getElementById("switch3");
                            console.log(input);
                            if (obj.checked) {
                                console.log("打开");
                                input.value = "1";
                            } else {
                                console.log("关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">推荐圈子开关</label>
                    <?php
                    if ($row['recommendOf']==1) {
                        echo '<input type="checkbox" name="recommendOf" id="switch3" value="1" data-switch="success"
                           onclick="myOnClickHandler4(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="recommendOf" id="switch3" value="0" data-switch="success"
                           onclick="myOnClickHandler4(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch3" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler2(obj) {
                            var input = document.getElementById("switch1");
                            console.log(input);
                            if (obj.checked) {
                                console.log("打开");
                                input.value = "1";
                            } else {
                                console.log("关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">快捷入口</label>
                    <?php
                    if ($row['kuaijie']==1) {
                        echo '<input type="checkbox" name="kuaijie" id="switch1" value="1" data-switch="success"
                           onclick="myOnClickHandler2(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="kuaijie" id="switch1" value="0" data-switch="success"
                           onclick="myOnClickHandler2(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch1" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler3(obj) {
                            var input = document.getElementById("switch2");
                            console.log(input);
                            if (obj.checked) {
                                console.log("打开");
                                input.value = "1";
                            } else {
                                console.log("关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">父级分类开关</label>
                    <?php
                    if ($row['fatherTitle']==1) {
                        echo '<input type="checkbox" name="fatherTitle" id="switch2" value="1" data-switch="success"
                           onclick="myOnClickHandler3(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="fatherTitle" id="switch2" value="0" data-switch="success"
                           onclick="myOnClickHandler3(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch2" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4" id="radiusBoxStyle">
                        <label for="validationCustom01">圈子轮廓圆角</label>
                        <select class="form-control" id="example-select" name="radiusBoxStyle">
                            <?php
                            if ($row['radiusBoxStyle']==1) {
                                echo '<option value="1" selected>微圆</option>
                                      <option value="2">椭圆</option>
                                       <option value="3">全圆</option>';
                            }else if($row['radiusBoxStyle']==2){
                                echo '<option value="1">微圆</option>
                                      <option value="2" selected>椭圆</option>
                                       <option value="3">全圆</option>';
                            }else{
                                 echo '<option value="1">微圆</option>
                                  <option value="2">椭圆</option>
                                      <option value="3" selected>全圆</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    <div class="form-group col-sm-4" id="radiusStyle">
                        <label for="validationCustom01">圈子LOGO圆角</label>
                        <select class="form-control" id="example-select" name="radiusStyle">
                            <?php
                            if ($row['radiusStyle']==1) {
                                echo '<option value="1" selected>微圆</option>
                                      <option value="2">椭圆</option>
                                       <option value="3">全圆</option>';
                            }else if($row['radiusStyle']==2){
                                echo '<option value="1">微圆</option>
                                      <option value="2" selected>椭圆</option>
                                       <option value="3">全圆</option>';
                            }else{
                                 echo '<option value="1">微圆</option>
                                  <option value="2">椭圆</option>
                                      <option value="3" selected>全圆</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="circlePagePost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<?php
include_once 'Footer.php';
?>

</body>
</html>