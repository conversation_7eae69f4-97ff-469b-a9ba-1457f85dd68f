$(document).ready(function() {
    "use strict";

    // 初始化第一个表格（basic-datatable）
    $("#basic-datatable").DataTable({
        keys: true, // 启用键盘快捷键
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>", // 上一页按钮的 HTML
                next: "<i class='mdi mdi-chevron-right'>" // 下一页按钮的 HTML
            }
        },
        
        drawCallback: function() {
            // 添加样式类到分页按钮
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        }
    });
     $("#basic-chat").DataTable({
        keys: true, // 启用键盘快捷键
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>", // 上一页按钮的 HTML
                next: "<i class='mdi mdi-chevron-right'>" // 下一页按钮的 HTML
            }
        },
        order: [[0, 'desc']], // 默认降序排序，根据第一列（索引为0）
        drawCallback: function() {
            // 添加样式类到分页按钮
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        }
    });
     $("#basic-meta").DataTable({
        keys: true, // 启用键盘快捷键
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>", // 上一页按钮的 HTML
                next: "<i class='mdi mdi-chevron-right'>" // 下一页按钮的 HTML
            }
        },
        order: [[1, 'asc']], // 默认降序排序，根据第一列（索引为0）
        drawCallback: function() {
            // 添加样式类到分页按钮
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        }
    });
        // 初始化第一个表格（basic-datatable）
     $("#basic-textlong-dx").DataTable({
      'select': {  
            'style': 'multi', // 允许多选  
            'selector': 'td:first-child', // 选择复选框的列  
            className: 'selected' // 为选中的行添加一个类名  
        },   
      'columnDefs': [  
            {  
                'targets': 0, // 选择复选框列的索引  
                'checkboxes': {  
                    'selectRow': true // 选中复选框时选择整行  
                }  
            }  
        ],
      keys: true, // 启用键盘快捷键
      language: {
        paginate: {
          previous: "<i class='mdi mdi-chevron-left'></i>", // 上一页按钮的 HTML
          next :"<i class='mdi mdi-chevron-right'></i>" // 下一页按钮的 HTML
        }
      },
      order: [[0, 'desc']], // 默认降序排序，根据第一列（索引为0）
      columnDefs: [{
        targets: [1,2], // 目标列的索引，这里假设是第一列
        render: function(data, type, row) {
          if (type === 'display' && data.length > 10) {
            return '<span title="' + data + '">' + data.substring(0, 10) + '...</span>';
          }
          return data;
        }
      }],
      drawCallback: function() {
        // 添加样式类到分页按钮
        $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
      }
    });
    $("#basic-textlong").DataTable({
      keys: true, // 启用键盘快捷键
      language: {
        paginate: {
          previous: "<i class='mdi mdi-chevron-left'></i>", // 上一页按钮的 HTML
          next :"<i class='mdi mdi-chevron-right'></i>" // 下一页按钮的 HTML
        }
      },
      order: [[0, 'desc']], // 默认降序排序，根据第一列（索引为0）
      columnDefs: [{
        targets: [0,1], // 目标列的索引，这里假设是第一列
        render: function(data, type, row) {
          if (type === 'display' && data.length > 10) {
            return '<span title="' + data + '">' + data.substring(0, 10) + '...</span>';
          }
          return data;
        }
      }],
      drawCallback: function() {
        // 添加样式类到分页按钮
        $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
      }
    });
     $("#basic-shop").DataTable({
      keys: true, // 启用键盘快捷键
      language: {
        paginate: {
          previous: "<i class='mdi mdi-chevron-left'></i>", // 上一页按钮的 HTML
          next :"<i class='mdi mdi-chevron-right'></i>" // 下一页按钮的 HTML
        }
      },
      order: [[0, 'desc']], // 默认降序排序，根据第一列（索引为0）
      columnDefs: [{
        targets: [1], // 目标列的索引，这里假设是第一列
        render: function(data, type, row) {
          if (type === 'display' && data.length > 10) {
            return '<span title="' + data + '">' + data.substring(0, 10) + '...</span>';
          }
          return data;
        }
      }],
      drawCallback: function() {
        // 添加样式类到分页按钮
        $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
      }
    });
    // 初始化第二个表格（datatable-buttons）
    var a = $("#datatable-buttons").DataTable({
        lengthChange: false, // 禁用每页显示记录数选项
        dom: 'Bfrtip',
        buttons: ['copy', 'csv', 'excel', 'pdf', 'print'], // 启用复制和打印按钮
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>", // 上一页按钮的 HTML
                next: "<i class='mdi mdi-chevron-right'>" // 下一页按钮的 HTML
            }
        },
        drawCallback: function() {
            // 添加样式类到分页按钮
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        }
    });
    // 初始化第三个表格（selection-datatable）
    $("#selection-datatable").DataTable({
        select: {
            style: "multi" // 启用多选模式
        },
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>", // 上一页按钮的 HTML
                next: "<i class='mdi mdi-chevron-right'>" // 下一页按钮的 HTML
            }
        },
        drawCallback: function() {
            // 添加样式类到分页按钮
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        }
    });

    // 将第二个表格的按钮添加到指定位置
    a.buttons().container().appendTo("#datatable-buttons_wrapper .col-md-6:eq(0)");
});