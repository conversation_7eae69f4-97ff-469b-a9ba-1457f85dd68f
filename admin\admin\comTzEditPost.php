<?php
session_start();
?>
<?php
include_once 'connect.php';
$id = $_POST['id'];
$text = $_POST['text'];
$uid = trim($_POST['uid']);
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $charu = "UPDATE typecho_forum_comment SET text = '$text', uid = '$uid' WHERE id = '$id'";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('修改成功');location.href = 'comTzAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');location.href = 'comTzAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
