<?php
session_start();
?>



<?php
include_once 'Nav.php';
$id = $_GET['id'];
$sql = "SELECT * FROM Sy_set";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}

$article = "SELECT * FROM typecho_shop WHERE id='$id' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);
$subtype = $mod['subtype'];
$vipjg = (float)$mod['vipDiscount']*(float)$mod['price'];
if ($vipjg==0) {
    $vipjg = '免费';
}else if($vipjg==(float)$mod['price']){
    $vipjg = '无优惠';
}else{
    $vipjg .= $row['Assetname'];
}

$withdrawals = "SELECT * FROM typecho_shoptype WHERE parent = '0' ORDER BY id DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
$withdrawals2 = "SELECT * FROM typecho_shoptype WHERE parent <> '0' ORDER BY id DESC";
$withdrawalsResult2 = mysqli_query($connect, $withdrawals2);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">审核商品</h4>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">商品id</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="id" value="<?php echo $id ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['uid'] ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">商品标题</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="title" value="<?php echo $mod['title'] ?>" readonly>
                    </div>
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">商品价格</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="price" value="<?php echo $mod['price'].$row['Assetname'] ?>" readonly>
                    </div>
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">商品库存</label>
                        <input type="number" class="form-control" id="validationCustom01"
                               name="num" value="<?php echo $mod['num'] ?>" readonly>
                    </div>
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">VIP购买价格</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="vipjg" value="<?php echo $vipjg ?>" readonly>
                    </div>
                     
                     <?php  
                function displayCategories($parent = 0, $level = 1) {  
                    global $connect;  
                    $id = $_GET['id'];
                    $query = "SELECT * FROM typecho_shoptype WHERE parent = $parent ORDER BY id DESC";  
                    $result = mysqli_query($connect, $query);  
                    $article = "SELECT * FROM typecho_shop WHERE id='$id' limit 1";
                    $resarticle = mysqli_query($connect, $article);
                    $mod = mysqli_fetch_array($resarticle);
                    $subtype = $mod['subtype'];
                    while ($category = mysqli_fetch_array($result)) {  
                        // Skip the top-level categories  
                        if ($level === 1) {  
                            continue; // Skip the current iteration  
                        }  
                        if ($subtype==$category['id']) {
                             echo '<option value="' . $category['id'] . '" selected>' . str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;', $level - 1)  . $category['name'] . '</option>';  
                        }else{
                             echo '<option value="' . $category['id'] . '">'. str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;', $level - 1)  . $category['name'] . '</option>';  
                        }
                       
                        if ($level < 3) {  
                            displayCategories($category['id'], $level + 1);  
                        }  
                    }  
                }  
                  
                $withdrawals = "SELECT * FROM typecho_shoptype WHERE parent = 0 ORDER BY id DESC";  
                $withdrawalsResult = mysqli_query($connect, $withdrawals);  
                ?>  
                <div class="form-group col-sm-4">  
                    <label for="validationCustom01">分类</label>  
                    <select class="form-control" id="example-select" name="subtype" readonly>
                        <?php  
                        // Fetch and display top-level categories, but make them disabled  
                        while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {  
                            echo '<option value="'.$withdrawal['id'].'" disabled>'.$withdrawal['name'].'</option>';  
                            displayCategories($withdrawal['id'], 2); // Start from level 2 for subcategories  
                        }  
                        ?>  
                    </select>  
                </div>
                     <div class="form-group mb-3">
                    <label for="yl" style="margin-top:.5rem">商品封面</label><br><span class="dtr-data" id="yl"><img style="width: 200px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="<?php echo $mod['imgurl'] ?>" class="spotlight"></span>
                    </div>
                    
                    <label for="validationCustom01">商品介绍<span class="badge badge-warning-lighten"style="font-size: 0.8rem;margin-left:10px">审核中不支持修改</span></label>
                    
                        <div id="editor—wrapper">
                        <div id="toolbar-container"></div>
                            <div id="editor-container"></div>
                             <textarea id="editorContent" name="text" style="display: none;"><?php echo $mod['text'] ?></textarea>  
                        </div>
                    <br>
                    <label for="validationCustom01">购买后显示<span class="badge badge-warning-lighten"style="font-size: 0.8rem;margin-left:10px">审核中不支持修改</span></label>
                    <div id="editor—wrapper">
                        <div id="toolbar-container2"></div>
                            <div id="editor-container2"></div>
                             <textarea id="editorContent2" name="value" style="display: none;"><?php echo $mod['value'] ?></textarea>  
                        </div>
                    <div class="form-group mb-3 text_right">
                        <a class="fabu" href="shopAuditPost.php?id=<?php echo $id ?>&status=Pass">
                            <button class="btn btn-primary" id="payoutPost" style="margin-right:10px">通过</button>
                        </a>
                        <a class="fabu" href="shopAuditPost.php?id=<?php echo $id ?>&status=Refuse">
                            <button class="btn btn-danger" id="payoutPost">拒绝</button>
                        </a>
                    </div>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>

<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->
<link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
<script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>
<script>
const { createEditor, createToolbar } = window.wangEditor;

// 第一个编辑器的配置
const editorConfig1 = {
    placeholder: '请输入内容...',
    onChange(editor) {
        const html = editor.getHtml();  
        document.getElementById('editorContent').value = html;  
    },
    MENU_CONF: {
        // 上传图片的配置
        uploadImage: {  
            server: '<?php echo $API_UPLOAD_FULL ?>',
            fieldName: 'file',
            maxFileSize: 50 * 1024 * 1024,
            maxNumberOfFiles: 10,
            allowedFileTypes: ['image/*'],
            meta: {
                 webkey: '<?php echo $api_key ?>'
            },
            metaWithUrl: false,
            onSuccess: function(file, res) {  
                toastr.success(`上传成功`, "提示"); 
            },  
            onFailed: function(file, res) {  
                toastr.error(`${res.message}`, "上传失败"); 
            },  
            onError: function(file, err, res) {  
                toastr.error(`${err} | ${res.message}`, "上传失败"); 
            }  
        },
        // 上传视频的配置
        uploadVideo: {  
            server: '<?php echo $API_UPLOAD_FULL ?>',
            fieldName: 'file',
            maxFileSize: 50 * 1024 * 1024,
            allowedFileTypes: ['video/*'],
            meta: {
                 webkey: '<?php echo $api_key ?>'
            },
            metaWithUrl: false,
            onSuccess: function(file, res) {  
                toastr.success(`上传成功`, "提示"); 
            },  
            onFailed: function(file, res) {  
                toastr.error(`${res.message}`, "上传失败"); 
            },  
            onError: function(file, err, res) {  
                toastr.error(`${err} | ${res.message}`, "上传失败"); 
            }  
        }
    }
};

// 创建第一个编辑器
const editor1 = createEditor({
    selector: '#editor-container',
    html: document.getElementById('editorContent').value,
    config: editorConfig1,
    mode: 'simple',
});

// 第一个编辑器的工具栏配置
const toolbarConfig1 = {
    toolbarKeys: [
    "blockquote",
    "header1",
    "header2",
    "header3",
    "|",
    "bold",
    "underline",
    "italic",
    "through",
    "color",
    "bgColor",
    "clearStyle",
    "|",
    "bulletedList",
    "numberedList",
    "justifyLeft",
    "justifyRight",
    "justifyCenter" ,
    "|",
    "insertLink",
    {
        key: 'group-image', 
        title: '图片', 
        iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>', 
        menuKeys:  ['insertImage', 'uploadImage']
        
    },
    {
        key: 'group-video', 
        title: '视频', 
        iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z"></path></svg>', 
        menuKeys: ['insertVideo', 'uploadVideo']
        
    },
    "insertTable",
    "codeBlock",
    "|",
    "undo",
    "redo"
]
};
// 创建第一个编辑器的工具栏
const toolbar1 = createToolbar({
    editor: editor1,
    selector: '#toolbar-container',
    config: toolbarConfig1,
    mode: 'simple',
});

// 第二个编辑器的配置，使用与第一个编辑器相似的配置
const editorConfig2 = Object.assign({}, editorConfig1, {
    onChange(editor) {
        const html = editor.getHtml();  
        document.getElementById('editorContent2').value = html;  
    },
    // 更改一些配置
});

// 创建第二个编辑器
const editor2 = createEditor({
    selector: '#editor-container2',
    html: document.getElementById('editorContent2').value,
    config: editorConfig2,
    mode: 'simple',
});

// 第二个编辑器的工具栏配置，也与第一个编辑器相似
const toolbarConfig2 = Object.assign({}, toolbarConfig1);

// 创建第二个编辑器的工具栏
const toolbar2 = createToolbar({
    editor: editor2,
    selector: '#toolbar-container2',
    config: toolbarConfig2,
    mode: 'simple',
});

</script>


<?php
include_once 'Footer.php';
?>

</body>
</html>