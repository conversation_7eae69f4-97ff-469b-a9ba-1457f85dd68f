<?php
session_start();
?>



<?php
include_once 'Nav.php';
$id = $_GET['id'];

$article = "SELECT * FROM typecho_space WHERE id='$id' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <?php if ($mod['type'] == '0'||$mod['type'] == '4') { ?>
                <h4 class="header-title mb-3 size_18">编辑动态</h4>
                <?php } ?>
                <?php if ($mod['type'] == '3') { ?>
                <h4 class="header-title mb-3 size_18">编辑评论</h4>
                <?php } ?>
                <form class="needs-validation" m action="spaceEditPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">id</label>
                        <input type="number" class="form-control" id="validationCustom01" value="<?php echo $id ?>" placeholder="id" name="id" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="number" class="form-control" id="validationCustom01" value="<?php echo $mod['uid'] ?>" placeholder="请输入发布者UID"
                               name="uid" required>
                    </div>
                    <?php if ($mod['type'] == '0'||$mod['type'] == '4') { ?>
                    <div class="form-group col-sm-4">
                        <label>动态类型</label>
                            <select class="form-control" id="dynamic-type" name="type">
                                 <?php
                                if ($mod['type'] == '0') {
                                    echo '<option value="0" selected>图文动态</option>';
                                } else {
                                    echo '<option value="0">图文动态</option>';
                                }
                                if ($mod['type'] == '4') {
                                    echo '<option value="4" selected>视频动态</option>';
                                } else {
                                    echo '<option value="4">视频动态</option>';
                                }
                                ?>
                            </select>
                    </div>
                    
                   <div class="form-group mb-3" id="validationCustom011">
                        <label>图片/视频链接 </label><span class="badge badge-success-lighten right_10"style="font-size: 0.8rem;">多图用||隔开</span>
                        <input type="text" class="form-control" id="picLinkInput" value="<?php echo $mod['pic'] ?>" placeholder="图片/视频链接" name="pic">
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler2(obj) {
                            var input = document.getElementById("switch1");
                            console.log(input);
                            if (obj.checked) {
                                console.log("仅自己可见打开");
                                input.value = "1";
                            } else {
                                console.log("仅自己可见关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">仅自己可见</label>
                    <?php
                    if ($mod['onlyMe']==1) {
                        echo '<input type="checkbox" name="only" id="switch1" value="1" data-switch="success"
                           onclick="myOnClickHandler2(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="only" id="switch1" value="0" data-switch="success"
                           onclick="myOnClickHandler2(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch1" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <label for="validationCustom01">动态内容</label>
                    <?php } ?>
                    <?php if ($mod['type'] == '3') { ?>
                    <label for="validationCustom01">评论内容</label>
                    <?php } ?>
                     
                        <textarea id="notice" class="form-control" rows="6" placeholder="输入内容" name="text" required><?php echo $mod['text'] ?></textarea>
                    <br />
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="spaceEditPost">保存</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function check() {
        let uid = document.getElementsByName('uid')[0].value.trim();
        let text = document.getElementsByName('text')[0].value.trim();
        if (uid.length == 0) {
            alert("作者UID不能为空");
            return false;
        } else if (text.length == 0) {
            alert("内容不能为空");
            return false;
        }

    }

</script>
<?php
include_once 'Footer.php';
?>

</body>
</html>