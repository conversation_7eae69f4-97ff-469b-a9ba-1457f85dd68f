<?php
session_start();
?>



<?php
include_once 'Nav.php';
$id = $_GET['id'];
$article = "SELECT * FROM Sy_icon WHERE id='$id' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">新增图标</h4>

                <form class="needs-validation" m action="iconEditPost.php" method="post" onsubmit="return check()"
                      novalidate>
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">图标id</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="id" value="<?php echo $id ?>" readonly>
                    </div>
                     <div class="form-group mb-3" id="validationCustom011">
                        <label>图标链接 <button type="button" id="uploadButton" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 修改图标</button></label>
                        <input type="text" class="form-control" id="picLinkInput" placeholder="图片链接" value="<?php echo $mod['url'] ?>" name="url" readonly>
                        <input type="file" id="uploadImage" accept="image/*" style="display: none;">
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">图标名称</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入图标名称"
                               name="name" value="<?php echo $mod['name'] ?>" required>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">转跳链接</label><a href="#" target="_blank"><span class="badge badge-success-lighten"style="font-size: 0.8rem;">点击查看配置教程</span></a>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入转跳链接"
                               name="link" value="<?php echo $mod['link'] ?>" required>
                    </div>
                    <div class="form-group col-sm-4">
                        <label>转跳限制</label>
                            <select class="form-control" id="dynamic-type" name="lgof">
                             <?php
                                if ($mod['lgof'] == 'true') {
                                    echo '<option value="true" selected>需登录</option>
                                    <option value="false">无限制</option>';
                                } else {
                                    echo '<option value="true">需登录</option>
                                    <option value="false" selected>无限制</option>';
                                }
                                ?>
                                    
                            </select>
                    </div>
                  
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="iconEditPost">保存修改</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function check() {
        let uid = document.getElementsByName('uid')[0].value.trim();
        let text = document.getElementsByName('text')[0].value.trim();
        let url = document.getElementsByName('url')[0].value.trim();
        if (uid.length == 0) {
            alert("图标名称不能为空");
            return false;
        } else if (text.length == 0) {
            alert("转跳链接不能为空");
            return false;
        } else if (url.length == 0) {
            alert("请上传图标");
            return false;
        }

    }
    // 获取上传图片按钮和文件上传输入框
    var uploadButton = document.getElementById("uploadButton");
    var uploadImage = document.getElementById("uploadImage");
    var picLinkInput = document.getElementById("picLinkInput");

uploadFiles(uploadButton, uploadImage, picLinkInput, 'null', 'null');
 <?php
 include_once 'uploadJs.php';
 ?>
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>