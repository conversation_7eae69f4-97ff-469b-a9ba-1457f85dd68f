<?php
session_start();
?>



<?php
include_once 'Nav.php';
$mid = $_GET['mid'];
$withdrawals = "SELECT * FROM typecho_metas WHERE type = 'category' ORDER BY mid DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
$withdrawals2 = "SELECT * FROM typecho_metas WHERE mid = '$mid'";
$withdrawalsResult2 = mysqli_query($connect, $withdrawals2);
$metadata = mysqli_fetch_array($withdrawalsResult2);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">新增分类标签</h4>

                <form class="needs-validation" action="metaEditPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">分类mid</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $mid ?>" placeholder="分类mid"
                               name="mid" readonly>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">名称</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $metadata['name']; ?>" placeholder="请输入名称"
                               name="name" required>
                    </div>
                     <div class="form-group mb-3">
                        <label for="validationCustom01">缩略名</label>
                        <input type="text" class="form-control" id="validationCustom01" value="<?php echo $metadata['slug']; ?>" placeholder="请输入缩略名"
                               name="slug" required>
                    </div>
                    <div class="form-group col-sm-4">
                        <label>类型</label>
                            <select class="form-control" id="dynamic-type" name="type" readonly>
                                <?php
                                if ($metadata['type'] == 'category') {
                                    echo '<option value="category" selected>分类</option>';
                                } else {
                                    echo '<option value="category">分类</option>';
                                }
                                if ($metadata['type'] == 'tag') {
                                    echo '<option value="tag" selected>标签</option>';
                                } else {
                                    echo '<option value="tag">标签</option>';
                                }
                                ?>
                                   
                                    
                            </select>
                    </div>
                    <?php
                    if ($metadata['type'] == 'category') {
                        echo '<div style="display:block;">';
                    } else {
                        echo '<div style="display:none;">';
                    }
                    ?>
                    
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">上级分类</label>
                            <select class="form-control" id="example-select" name="parent">
                                <option value="0" <?php if($metadata['parent'=='0']){echo "selected";} ?>>无父级</option>
                                <?php
                                while ($withdrawal = mysqli_fetch_array($withdrawalsResult)) {
                                    ?>
                                    <option value="<?php echo $withdrawal['mid']; ?>" <?php if($withdrawal['mid']==$metadata['parent']){echo "selected";} ?>><?php echo $withdrawal['name'] ?></option>
                                <?php
                                }
                                ?>
                            </select>
                    </div>
                     </div>
                    <div style="display:block;" style="display:none;" id="video">
                        
                    </div>
        
                    
                    <label for="validationCustom01">简介</label>
                        <textarea id="notice" class="form-control" rows="6" name="description" placeholder="请输入简介" required><?php echo $metadata['description']; ?></textarea>
                    <br />
                    <div class="form-group mb-3" id="validationCustom011">
                        <label>缩略图(选填) <button type="button" id="uploadButton" class="btn btn-success2 btn-sm btn-rounded right_10"><i class="dripicons-upload"></i> 上传图片</button></label>
                        <input type="text" class="form-control" style="display: none;" id="picLinkInput" placeholder="图片链接" name="imgurl" value="<?php echo $metadata['imgurl']; ?>" readonly>
                        <input type="file" id="uploadImage" accept="image/*" style="display: none;">
                        <br><span class="dtr-data" id="logo1"><img style="width: 200px;object-fit: cover;margin-top:20px;margin-right:20px;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="<?php echo $metadata['imgurl'] ?>" id="picLinkInput1" class="spotlight"></span>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">排序（越小越靠前）</label>
                        <input type="number" class="form-control" id="validationCustom01" placeholder="请输入排序" value="<?php echo $metadata['order']; ?>"
                               name="order" required>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-primary" type="submit" id="metaEdithPost">创建</button>
                    </div>
                </form>

            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function check() {
        let name = document.getElementsByName('name')[0].value.trim();
        let slug = document.getElementsByName('slug')[0].value.trim();
        let description = document.getElementsByName('description')[0].value.trim();
        let order = document.getElementsByName('order')[0].value.trim();
        if (name.length == 0) {
            alert("名称不能为空");
            return false;
        } else if (slug.length == 0) {
            alert("缩略名不能为空");
            return false;
        } else if (description.length == 0) {
            alert("描述不能为空");
            return false;
        } else if (order.length == 0) {
            alert("排序不能为空");
            return false;
        }
        

    }
    // 获取上传图片按钮和文件上传输入框
    var uploadButton = document.getElementById("uploadButton");
    var uploadImage = document.getElementById("uploadImage");
    var picLinkInput = document.getElementById("picLinkInput");
     var picLinkInput1 = document.getElementById("picLinkInput1");

uploadFiles(uploadButton, uploadImage, picLinkInput, picLinkInput1, 'null');
 <?php
 include_once 'uploadJs.php';
 ?>

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>