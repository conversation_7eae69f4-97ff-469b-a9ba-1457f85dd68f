<?php
session_start();
?>

<?php
$number = intval($_POST['code']);
$time = gmdate("Y-m-d H:i:s", time() + 8 * 3600);
$file = $_SERVER['PHP_SELF'];
include_once 'connect.php';
function generateRandomString($length) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $randomString;
}
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    for ($j = 1; $j <= $number; $j++) {
        $code = generateRandomString(8);
        $created = time();
    
        $sql = "INSERT INTO typecho_invitation (code, created, uid,status) VALUES ('$code', '$created', '0', '0')";
    
        if ($connect->query($sql) === TRUE) {
             echo "<script>alert('生成成功');location.href = 'codeAdmin.php';</script>";
        } else {
            echo "<script>alert('生成失败');location.href = 'codeAdmin.php';</script>";   
        }
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}

