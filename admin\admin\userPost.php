<?php
session_start();
?>

<?php
$adminName = trim($_POST['adminName']);
$pw = trim($_POST['pw']);
$file = $_SERVER['PHP_SELF'];
include_once 'connect.php';
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }

        if ($pw) {
            $loginsql = "update Sy_login set user = '$adminName' ,pw ='" . md5($pw) . "' where id = '1'";
            session_destroy();
        } else {
            $loginsql = "update Sy_login set user = '$adminName'  where id = '1'";
        }
        $loginresult = mysqli_query($connect, $loginsql);
        if ($loginresult) {
            echo "<script>alert('修改成功');location.href = 'login.php';</script>";
        } else {
            echo "<script>alert('修改失败');location.href = 'login.php';</script>";
        }

} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}

