<?php
session_start();
?>

<?php
include_once 'Nav.php';
include_once 'connect.php';
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">添加用户</h4>

                <form class="needs-validation" action="userAddPost.php" method="post" novalidate>
                    <div class="form-group mb-3">
                        <label for="validationCustom04">用户名</label>
                        
                        <input type="text" class="form-control"  placeholder="输入用户名"
                               name="name" required>
                    </div>
                     <div class="form-group mb-3">
                        <label for="validationCustom04">用户昵称</label>
                        
                        <input type="text" class="form-control"  placeholder="用户昵称（选填）"
                               name="screenName" >
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom04">邮箱</label>
                        
                        <input type="email" class="form-control"  placeholder="邮箱"
                               name="mail" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom04">网址</label>
                        
                        <input type="url" class="form-control"  placeholder="网址（选填）"
                               name="url" >
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom04">头衔</label>
                        
                        <input type="customize" class="form-control"  placeholder="为用户自定义头衔（选填）"
                               name="customize" >
                    </div>
                      <div class="form-group col-sm-4">
                        <label for="validationCustom01">用户组</label>
                            <select class="form-control" id="example-select" name="group">
                                <option value="administrator">管理员</option>
                                <option value="editor">编辑</option>
                                <option value="contributor" selected>贡献者</option>
                                <option value="subscriber">关注者</option>
                                <option value="visitor">游客</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">密码</label> <span class="badge badge-success-lighten"style="font-size: 0.8rem;">默认密码为123456</span>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success"  type="submit" id="userAddPost">提交</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  


</div>


<?php
include_once 'Footer.php';
?>

</body>
</html>