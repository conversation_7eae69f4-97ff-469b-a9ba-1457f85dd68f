<?php
session_start();
?>

<?php
include_once 'connect.php';
$uid = $_POST['uid'];
$assest = $_POST['assest'];
$way = $_POST['way'];
$file = $_SERVER['PHP_SELF'];

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($way == 'add') {
        $sql_1=mysqli_query($connect,"select * from typecho_users where uid='$uid'");
        $users=mysqli_fetch_array($sql_1);
        $assets=$users['assets']+$assest;
        $sql_2=mysqli_query($connect,"UPDATE `typecho_users` SET `assets`='$assets' WHERE uid='$uid'");
        $total_amount = $assest;
        $out_trade_no = time() . "assetsAdmin";
        $paytype = "assetsAdmin";
        $created = time();
        $insertQuery = "INSERT INTO typecho_paylog (subject, total_amount, out_trade_no, paytype, uid, created, status)
                        VALUES ('管理员充值', '$total_amount', '$out_trade_no',  '$paytype', '$uid', '$created', '1')";
        $sql_3 = mysqli_query($connect, $insertQuery);
        if ($sql_1&&$sql_2&&$sql_3) {
            echo "<script>alert('充值成功');history.back();</script>";
        } else {
            echo "<script>alert('充值失败')';history.back();</script>";
        }
    } else if($way == 'sub'){
        $sql_1=mysqli_query($connect,"select * from typecho_users where uid='$uid'");
        $users=mysqli_fetch_array($sql_1);
        $assets=$users['assets']-$assest;
        $sql_2=mysqli_query($connect,"UPDATE `typecho_users` SET `assets`='$assets' WHERE uid='$uid'");
        $total_amount = $assest;
        $out_trade_no = time() . "assetsAdmin";
        $paytype = "assetsAdmin";
        $created = time();
        $insertQuery = "INSERT INTO typecho_paylog (subject, total_amount, out_trade_no, paytype, uid, created, status)
                        VALUES ('管理员扣除', '-$total_amount', '$out_trade_no',  '$paytype', '$uid', '$created', '1')";
        $sql_3 = mysqli_query($connect, $insertQuery);
        if ($sql_1&&$sql_2&&$sql_3) {
            echo "<script>alert('扣除成功');history.back();</script>";
        } else {
            echo "<script>alert('扣除失败')';history.back();</script>";
        }
    } else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}