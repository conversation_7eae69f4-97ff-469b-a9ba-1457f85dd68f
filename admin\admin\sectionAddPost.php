<?php
session_start();
?>
<?php

include_once 'connect.php';
$name = $_POST['name'];
$slug = $_POST['slug'];
$type = $_POST['type'];
$parent = $_POST['parent'];
$text = $_POST['text'];
$pic = $_POST['pic'];
$bg = $_POST['bg'];
$restrict = $_POST['restrict'];
$order = $_POST['order'];
if ($type=='sort') {
    $parent = '0';
    $text = '';
}
if (empty($text)) {
    $text = NULL;
}
if (empty($pic)) {
    $pic = NULL;
}
if (empty($bg)) {
    $bg = NULL;
}
if (empty($parent)) {
    $parent = '0';
}

    


$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
   if ($type=='sort') {
       $charu = "insert into typecho_forum_section (`name`,`type`,`parent`,`order`,`slug`,`text`) values ('$name','$type','$parent','$order','','')";
    }else{
       $charu = "insert into typecho_forum_section (`name`,`slug`,`type`,`parent`,`text`,`pic`,`order`,`bg`,`restrict`) values ('$name','$slug','$type','$parent','$text','$pic','$order','$bg','$restrict')";
    }
    $result = mysqli_query($connect, $charu);
    if ($result) {
        echo "<script>alert('创建成功');location.href = 'sectionAdmin.php';</script>";
    } else {
        echo "<script>alert('创建失败');history.back();</script>";   
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
