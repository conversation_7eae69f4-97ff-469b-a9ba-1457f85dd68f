<?php
session_start();
?>

<?php
include_once 'connect.php';
$ipkiki = "select * from typecho_paykey order by id desc";
$ipki = mysqli_query($connect, $ipkiki);
?>

<?php
include_once 'Nav.php';
?>

<link href="/admin/assets/css/vendor/dataTables.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/responsive.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/buttons.bootstrap4.css" rel="stylesheet" type="text/css"/>
<link href="/admin/assets/css/vendor/select.bootstrap4.css" rel="stylesheet" type="text/css"/>
<!-- third party css end -->


<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">卡密管理</h4>
                <br><a class="fabu" onclick="deleteAll()">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded left_10">
                            <i class="mdi mdi-delete-empty mr-1"></i> 清空全部
                        </button>
                    </a><a class="fabu" onclick="deleteStatus()">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded left_10">
                            <i class="mdi mdi-delete-empty mr-1"></i> 删除已用
                        </button>
                    </a><a class="fabu" href="kmAdd.php">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded left_10">
                            <i class="dripicons-upload"></i> 生成卡密
                        </button>
                    </a><br><br>
                <table id="datatable-buttons" class="table dt-responsive nowrap" width="100%">
                    <thead>
                    <tr>
                        <th>id</th>
                        <th>卡密</th>
                        <th>生成时间</th>
                        <th>面值</th>
                        <th>使用情况</th>
                        <th style="width: 125px;">操作</th>
                    </tr>
                    </thead>

                    <tbody>
                    <?php
                    while ($IPinfo = mysqli_fetch_array($ipki)) {
                        ?>
                        <tr>
                            <td><?php echo $IPinfo['id'] ?></td>
                            <td>
                                <?php echo $IPinfo['value'] ?>
                            </td>
                            <td>
                                <small class="text-muted"><?php 
                                $timestamp = $IPinfo['created'];
                                $date = date("Y-m-d H:i:s", $timestamp);
                                echo $date?></small>
                                
                            </td>
                            <td><?php echo $IPinfo['price'] ?></td>
                            <td>
                                <h5>
                                    <?php if ($IPinfo['status']==1) { ?><span class="badge badge-danger-lighten">UID:<?php echo $IPinfo['uid'] ?>已使用</span><?php } else { ?><span class="badge badge-success-lighten">未使用<?php } ?></span>
                                </h5>
                            </td>
                            <td>
                                <a href="javascript:del(<?php echo $IPinfo['id']; ?>);">
                                    <button style="white-space: nowrap;" type="button"
                                            class="btn btn-danger btn-rounded">
                                        <i class=" mdi mdi-delete-empty mr-1"></i>删除
                                    </button>
                                </a>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>  
        </div>  
    </div> 
</div>


<script>
    function del(id) {
        if (confirm('您确认要删除id为' + id + '的卡密吗？')) {
            location.href = 'kmDel.php?id=' + id ;
        }
    }
    function deleteAll() {
        if (confirm('您确认要清空卡密吗？')) {
            location.href = 'kmDel.php?id=all';
        }
    }
    function deleteStatus() {
        if (confirm('您确认要删除已使用的卡密？')) {
            location.href = 'kmDel.php?id=status';
        }
    }
    
</script>


<?php
include_once 'Footer.php';
?>

<!-- third party js -->
<script src="/admin/assets/js/vendor/jquery.dataTables.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.bootstrap4.js"></script>
<script src="/admin/assets/js/vendor/dataTables.responsive.min.js"></script>
<script src="/admin/assets/js/vendor/responsive.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.buttons.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.bootstrap4.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.html5.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.flash.min.js"></script>
<script src="/admin/assets/js/vendor/buttons.print.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.keyTable.min.js"></script>
<script src="/admin/assets/js/vendor/dataTables.select.min.js"></script>
<!-- third party js ends -->
<!-- demo app -->
<script src="/admin/assets/js/pages/demo.datatable-init.js"></script>
<!-- end demo js-->


</body>
</html>