<?php
session_start();
?>



<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_pages";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">发现页设置</h4>
                <form class="needs-validation" action="findPagePost.php" method="post" onsubmit="return check()" novalidate>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler13(obj) {
                                var input = document.getElementById("switch13");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">图标模块</label>
                            <?php
                            if ($row['Hyperlinks']==1) {
                                echo '<input type="checkbox" name="Hyperlinks" id="switch13" value="1" data-switch="success"
                               onclick="myOnClickHandler13(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Hyperlinks" id="switch13" value="0" data-switch="success"
                               onclick="myOnClickHandler13(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch13" data-on-label="显示"
                               data-off-label="隐藏"></label>
                    </div>
                    
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler11(obj) {
                                var input = document.getElementById("switch11");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">活跃用户</label>
                            <?php
                            if ($row['Gallery']==1) {
                                echo '<input type="checkbox" name="Gallery" id="switch11" value="1" data-switch="success"
                               onclick="myOnClickHandler11(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Gallery" id="switch11" value="0" data-switch="success"
                               onclick="myOnClickHandler11(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch11" data-on-label="显示"
                               data-off-label="隐藏"></label>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler22(obj) {
                            var input = document.getElementById("switch21");
                            console.log(input);
                            if (obj.checked) {
                                console.log("打开");
                                input.value = "1";
                            } else {
                                console.log("关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">推荐文章</label>
                    <?php
                    if ($row['Findtop']==1) {
                        echo '<input type="checkbox" name="Findtop" id="switch21" value="1" data-switch="success"
                           onclick="myOnClickHandler22(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="Findtop" id="switch21" value="0" data-switch="success"
                           onclick="myOnClickHandler22(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch21" data-on-label="显示"
                           data-off-label="隐藏"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler12(obj) {
                                var input = document.getElementById("switch12");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.value = "1";
                                } else {
                                    console.log("关闭");
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">标签云</label>
                            <?php
                            if ($row['Code']==1) {
                                echo '<input type="checkbox" name="Code" id="switch12" value="1" data-switch="success"
                               onclick="myOnClickHandler12(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Code" id="switch12" value="0" data-switch="success"
                               onclick="myOnClickHandler12(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch12" data-on-label="显示"
                               data-off-label="隐藏"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler1(obj) {
                                var input = document.getElementById("switch1");
                                var PaymentMethods = document.getElementById("PaymentMethods");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("发现页轮播图打开");
                                    input.value = "1";
                                    PaymentMethods.style.display = "block";
                                } else {
                                    console.log("发现页轮播图关闭");
                                    input.value = "0";
                                    PaymentMethods.style.display = "none";
                                }
                            }
                        </script>
                        <label for="validationCustom01">发现页广告轮播图</label><span class="badge badge-success-lighten" style="font-size: 0.8rem;margin-left:10px">该功能只隐藏发现页轮播图，要彻底关闭请把广告轮播图显示数量设置为0</span>
                            <?php
                            if ($row['Bannerswitch']==1) {
                                echo '<input type="checkbox" name="Bannerswitch" id="switch1" value="1" data-switch="success"
                               onclick="myOnClickHandler1(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Bannerswitch" id="switch1" value="0" data-switch="success"
                               onclick="myOnClickHandler1(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch1" data-on-label="显示"
                               data-off-label="隐藏"></label>
                    </div>
                            
                   
                     <div class="form-group col-sm-4" style="width:65%">
                      <label for="Bannernumber">广告轮播图显示数量</label>
                      <div class="d-flex align-items-center">
                        <input name="Bannernumber" class="form-control" type="number" required="" id="Bannernumber" placeholder="0~6" style="flex: 1;" value="<?php echo $row['Bannernumber']; ?>">
                        <span style="margin-left: 15px;">张</span>
                      </div>
                    </div>
                    <div class="form-group mb-3">
                          <label for="Banner">轮播图一链接：</label>
                          <input name="Bannerimg1" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Bannerimg1']; ?>">
                          <label for="Banner">转跳链接：</label>
                          <input name="Bannerurl1" class="form-control" type="url" required="" id="Banner" placeholder="转跳链接" value="<?php echo $row['Bannerurl1']; ?>">
                    <?php
                    if ($row['Bannerimg1'] !== '') {
                        echo '<label for="yl">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 220px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Bannerimg1'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    
                    <div class="form-group mb-3">
                          <label for="Banner">轮播图二链接：</label>
                          <input name="Bannerimg2" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Bannerimg2']; ?>">
                          <label for="Banner">转跳链接：</label>
                          <input name="Bannerurl2" class="form-control" type="url" required="" id="Banner" placeholder="转跳链接" value="<?php echo $row['Bannerurl2']; ?>">
                    <?php
                    if ($row['Bannerimg2'] !== '') {
                        echo '<label for="yl">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 220px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Bannerimg2'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    <div class="form-group mb-3">
                          <label for="Banner">轮播图三链接：</label>
                          <input name="Bannerimg3" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Bannerimg3']; ?>">
                          <label for="Banner">转跳链接：</label>
                          <input name="Bannerurl3" class="form-control" type="url" required="" id="Banner" placeholder="转跳链接" value="<?php echo $row['Bannerurl3']; ?>">
                    <?php
                    if ($row['Bannerimg3'] !== '') {
                        echo '<label for="yl">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 220px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Bannerimg3'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    <div class="form-group mb-3">
                          <label for="Banner">轮播图四链接：</label>
                          <input name="Bannerimg4" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Bannerimg4']; ?>">
                          <label for="Banner">转跳链接：</label>
                          <input name="Bannerurl4" class="form-control" type="url" required="" id="Banner" placeholder="转跳链接" value="<?php echo $row['Bannerurl4']; ?>">
                    <?php
                    if ($row['Bannerimg4'] !== '') {
                        echo '<label for="yl">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 220px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Bannerimg4'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    <div class="form-group mb-3">
                          <label for="Banner">轮播图五链接：</label>
                          <input name="Bannerimg5" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Bannerimg5']; ?>">
                          <label for="Banner">转跳链接：</label>
                          <input name="Bannerurl5" class="form-control" type="url" required="" id="Banner" placeholder="转跳链接" value="<?php echo $row['Bannerurl5']; ?>">
                    <?php
                    if ($row['Bannerimg5'] !== '') {
                        echo '<label for="yl">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 220px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Bannerimg5'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    <div class="form-group mb-3">
                          <label for="Banner">轮播图六链接：</label>
                          <input name="Bannerimg6" class="form-control" type="url" required="" id="Banner" placeholder="图片链接" value="<?php echo $row['Bannerimg6']; ?>">
                          <label for="Banner">转跳链接：</label>
                          <input name="Bannerurl6" class="form-control" type="url" required="" id="Banner" placeholder="转跳链接" value="<?php echo $row['Bannerurl6']; ?>">
                    <?php
                    if ($row['Bannerimg6'] !== '') {
                        echo '<label for="yl">预览图：</label><br><span class="dtr-data" id="yl"><img style="width: 220px;height: 130px;object-fit: cover;box-shadow: 0 8px 12px #c9cbcfd6;border-radius: 6px" src="' . $row['Bannerimg6'] . '" class="spotlight"></span></div>';
                    } else {
                        echo '</div>';
                    }
                    ?>
                    
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="findPagePost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->
<script>
function check() {
  let Bannernumber = document.getElementsByName('Bannernumber')[0].value.trim();
  
  // 验证输入的值是否在1~6之间
  if (Bannernumber < 0 || Bannernumber > 6) {
    // 显示提示信息
    alert('轮播图显示数量在0~6之间！');
    // 阻止表单的提交
    return false;
  }
}
</script>



<?php
include_once 'Footer.php';
?>

</body>
</html>