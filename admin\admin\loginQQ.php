<?php
session_start();
?>


<?php
include_once 'Nav.php';

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));
$response = curl_exec($curl);
$responseData = json_decode($response, true);  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $qqAppletsAppid = $responseData['data']['qqAppletsAppid'];  
    $qqAppletsSecret = $responseData['data']['qqAppletsSecret'];  
} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">QQ配置</h4>
                <form class="needs-validation" action="loginQQPost.php" method="post"
                      novalidate>
                    <div class="form-group mb-3">
                          <p>在这里配置QQ登录和小程序相关信息。</p>
                    </div>
                    <div class="form-group mb-3">
                          <label for="qqAppletsAppid">QQ小程序APPID
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              可不填，负责QQ小程序登录
                          </span></label>
                          <input name="qqAppletsAppid" class="form-control" type="text" id="qqAppletsAppid" placeholder="请输入QQ小程序APPID" value="<?php echo $qqAppletsAppid;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="qqAppletsSecret">QQ小程序Secret
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              可不填，负责QQ小程序登录
                          </span></label>
                          <input name="qqAppletsSecret" class="form-control" type="text" id="qqAppletsSecret" placeholder="请输入QQ小程序Secret" value="<?php echo $qqAppletsSecret;  ?>">
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="loginQQPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<?php
include_once 'Footer.php';
?>

</body>
</html>