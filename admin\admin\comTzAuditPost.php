<?php
session_start();
?>

<?php
include_once 'connect.php';
$ids = isset($_GET['ids']) ? explode(',', $_GET['ids']) : array(); 
$id = $_GET['id'];
$status = $_GET['status'];
$file = $_SERVER['PHP_SELF'];
$time = time();
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($status === 'Pass') {
        $sql = "UPDATE typecho_forum_comment SET status = '1' WHERE id = '$id'"; 
        $sql2 = "SELECT * FROM typecho_forum_comment WHERE id='$id' limit 1"; 
        $result = mysqli_query($connect, $sql);
        $result2 = mysqli_query($connect, $sql2);
        $mod = mysqli_fetch_array($result2);
        $forumid = $mod['forumid'];
        $sql3 = "UPDATE typecho_forum SET replyTime = '$time' WHERE id = '$forumid'"; 
        $result3 = mysqli_query($connect, $sql3);
		$sqlSelect = "SELECT commentsNum FROM typecho_forum WHERE id = '$forumid'";  
		$result5 = mysqli_query($connect, $sqlSelect);  
		$row = mysqli_fetch_assoc($result5);  
		$commentsNumStr = $row['commentsNum']+1;
		$sql4 = "UPDATE typecho_forum SET commentsNum = '$commentsNumStr' WHERE id = '$forumid'";
		$result4 = mysqli_query($connect, $sql4);
        if ($result&&$result3&&result4&&result5) {
            echo "<script>alert('通过成功');location.href = 'comTzAdmin.php';</script>";
        } else {
            echo "<script>alert('通过失败')';history.back();</script>";
        }
    }else if($status == 'selected'){
        $ids = isset($_GET['ids']) ? explode(',', $_GET['ids']) : array();  
      
        if (empty($ids)) {  
            echo "<script>alert('请至少选择一行进行删除。');history.back();</script>";  
            exit;  
        }  
      
        $placeholders = implode(',', array_fill(0, count($ids), '?'));  
        $sql = "DELETE FROM `typecho_forum_comment` WHERE `id` IN ($placeholders)";  
      
        $stmt = $connect->prepare($sql);  
        if ($stmt === false) {  
            die('Prepare statement failed: ' . $connect->error);  
        }  
      
        $bindResult = $stmt->bind_param(str_repeat('i', count($ids)), ...$ids);  
        if ($bindResult === false) {  
            die('Binding parameters failed: ' . $stmt->error);  
        }  
      
        $executeResult = $stmt->execute();  
        if ($executeResult === true) {  
            $affectedRows = $stmt->affected_rows;  
            if($affectedRows==0){
                $affectedRows==1;
            }
            echo "<script>alert('删除成功，共删除了 {$affectedRows} 条评论');history.back();</script>";  
        } else {  
            echo "<script>alert('删除失败');history.back();</script>";  
        }  
   } else if ($status == 'plPass') {  
        if (empty($ids)) {  
            echo "<script>alert('请至少选择一行');history.back();</script>";  
            exit;  
        }  
        
        // 批量更新评论状态  
        $placeholders = implode(',', array_fill(0, count($ids), '?'));  
        $sql = "UPDATE `typecho_forum_comment` SET `status` = '1' WHERE `id` IN ($placeholders)";  
        $stmt = $connect->prepare($sql);  
        if ($stmt === false) {  
            die('Prepare statement failed: ' . $connect->error);  
        }  
        $bindResult = $stmt->bind_param(str_repeat('i', count($ids)), ...$ids);  
        if ($bindResult === false) {  
            die('Binding parameters failed: ' . $stmt->error);  
        }  
        $executeResult = $stmt->execute();  
        if ($executeResult === true) {  
            // 更新成功，现在更新所属文章的回复时间  
            $updatedArticles = array(); // 用来记录已经更新过的文章ID，避免重复更新  
            foreach ($ids as $id) {  
                $sql2 = "SELECT `forumid` FROM `typecho_forum_comment` WHERE `id` = ? LIMIT 1";  
                $stmt2 = $connect->prepare($sql2);  
                if ($stmt2 === false) {  
                    die('Prepare statement failed: ' . $connect->error);  
                }  
                $stmt2->bind_param('i', $id);  
                $stmt2->execute();  
                $result = $stmt2->get_result();  
                $forum = $result->fetch_assoc();  
				$forumid = $forum['forumid']; 
                if ($forum && !in_array($forum['forumid'], $updatedArticles)) {  
					$sqlSelect = "SELECT commentsNum FROM typecho_forum WHERE id = '$forumid'";
					$result5 = mysqli_query($connect, $sqlSelect);  
					$row = mysqli_fetch_assoc($result5);  
					$commentsNumStr = $row['commentsNum']+1;
					$sql4 = "UPDATE typecho_forum SET commentsNum = '$commentsNumStr' WHERE id = '$forumid'";
					$result4 = mysqli_query($connect, $sql4);
                    $sql3 = "UPDATE `typecho_forum` SET `replyTime` = ? WHERE `id` = ?";  
                    $stmt3 = $connect->prepare($sql3);  
                    if ($stmt3 === false) {  
                        die('Prepare statement failed: ' . $connect->error);  
                    }  
                    $stmt3->bind_param('ii', $time, $forum['forumid']);  
                    $updateResult = $stmt3->execute();  
                    if (!$updateResult) {  
                        // 如果更新文章回复时间失败，记录错误  
                        error_log("Failed to update reply time for forum {$forum['forumid']}: " . $stmt3->error);  
                    } else {  
                        $updatedArticles[] = $forum['forumid']; // 记录已更新的文章ID  
                    }  
                }  
            }  
            echo "<script>alert('操作成功，共通过了 {$stmt->affected_rows} 条评论');location.href = 'comTzAdmin.php';</script>";  
        } else {  
            echo "<script>alert('操作失败');history.back();</script>";  
        }  
    }else if($status == 'plRefuse'){  
        $ids = isset($_GET['ids']) ? explode(',', $_GET['ids']) : array();    
        
        if (empty($ids)) {    
            echo "<script>alert('请至少选择一行');history.back();</script>";    
            exit;    
        }    
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));  
        $sql = "UPDATE `typecho_forum_comment` SET `status` = '0' WHERE `id` IN ($placeholders)";  
        
        $stmt = $connect->prepare($sql);  
        if ($stmt === false) {    
            die('Prepare statement failed: ' . $connect->error);    
        }    
        
        $bindResult = $stmt->bind_param(str_repeat('i', count($ids)), ...$ids);    
        if ($bindResult === false) {    
            die('Binding parameters failed: ' . $stmt->error);    
        }    
        
        $executeResult = $stmt->execute();    
        if ($executeResult === true) {    
            $affectedRows = $stmt->affected_rows;    
            echo "<script>alert('操作成功，共拒绝了 {$affectedRows} 条评论');history.back();</script>";    
        } else {    
            echo "<script>alert('操作成功');history.back();</script>";    
        }    
    } else if($status === 'Refuse'){
        $sql = "UPDATE typecho_forum_comment SET status = '0' WHERE id = '$id'"; 
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('驳回成功');location.href = 'comTzAdmin.php';</script>";
        } else {
            echo "<script>alert('驳回失败');history.back();</script>";
        }
    } else if ($status === 'Del') {
        $sql = "DELETE FROM typecho_forum_comment WHERE id = '$id'";
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('删除成功');location.href = 'comTzAdmin.php';</script>";
        } else {
            echo "<script>alert('删除失败')';history.back();</script>";
        }
    }  else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}