<?php
session_start();
?>
<?php
include_once 'connect.php';
$uid = trim($_POST['uid']);
$pic = $_POST['pic'];
$video = $_POST['video'];
if ($_POST['type']=='pic'){
    if (empty($pic)) {
        $pic = NULL;
    }
    $link = $pic;
    $type = '0';
} else {
    if (empty($video)) {
        $video = NULL;
        echo "<script>alert('视频链接不能为空');location.href = 'spaceAdd.php';</script>";   
    }
    $link = $video;
    $type = '4';
}

$text = $_POST['text'];
$only = isset($_POST['only']) ? $_POST['only'] : 0;
$status = '1';

$uid = trim($_POST['uid']);
$time = time();
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $charu = "insert into typecho_space (uid,created,modified,text,pic,type,status,onlyMe) values ('$uid','$time','$time','$text','$link','$type','$status','$only')";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('发布成功');location.href = 'spaceAdmin.php';</script>";
        } else {
            echo "<script>alert('发布失败');location.href = 'spaceAdmin.php';</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
