<?php
session_start();
?>



<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_set";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}
$sql2 = "SELECT * FROM typecho_apiconfig";
$result2 = mysqli_query($connect, $sql2);
// 检查查询结果是否为空
if (mysqli_num_rows($result2) > 0) {
    // 获取第一行数据作为结果集
    $row2 = mysqli_fetch_assoc($result2);
}
//api
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);
$responseData = json_decode($response, true);  

if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $postMax = $responseData['data']['postMax'];    
    $isLogin = $responseData['data']['isLogin'];
    $webinfoTitle = $responseData['data']['webinfoTitle'];
} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">全局设置</h4>

                <form class="needs-validation" action="settingsPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group mb-3">
                          <label for="webinfoTitle">站点名称
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              用于邮件发送、消息推送等显示的名称，可以填写网站或APP的名称
                          </span></label>
                          <input name="webinfoTitle" class="form-control" type="text" required="" id="webinfoTitle" placeholder="请输入站点名称" value="<?php echo $webinfoTitle;  ?>">
                    </div>
                    <div class="form-group mb-3">
                          <label for="postMax">每日最大发布数量
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                             针对文章，动态，帖子等
                          </span></label>
                          <input name="postMax" class="form-control" type="text" required="" id="postMax" placeholder="输入每日最大发布数量" value="<?php echo $postMax ?>">
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler18(obj) {
                                var input = document.getElementById("swith22");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打开");
                                    input.setAttribute("value", "1");
                                } else {
                                    console.log("关闭");
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">H5开关（关闭后H5无法正常使用）</label>
                        <?php
                        if ($row['h5of']==1) {
                            echo '<input type="checkbox" name="h5of" id="swith22" value="1" data-switch="success"
                               onclick="myOnClickHandler18(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="h5of" id="swith22" value="0" data-switch="success"
                               onclick="myOnClickHandler18(this)">';
                        }
                        ?>

                        <label id="switchurl" style="display:block;" for="swith22" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <label for="Groupurl">交流群链接</label>
                        <input name="Groupurl" class="form-control" type="text" required="" id="Groupurl" placeholder="交流群链接" value="<?php echo $row['Groupurl']; ?>">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="Auditurl">审核员链接</label>
                        <input name="Auditurl" class="form-control" type="text" required="" id="Auditurl" placeholder="审核员链接" value="<?php echo $row['Auditurl']; ?>">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="Waiterurl">客服链接</label>
                        <input name="Waiterurl" class="form-control" type="text" required="" id="Waiterurl" placeholder="客服链接" value="<?php echo $row['Waiterurl']; ?>">
                    </div>
                    
                    <div class="form-group col-sm-4" style="display: none;">
                    
                        <label for="validationCustom01">全局UI风格</label>
                        <select class="form-control" id="example-select" name="homeStyle">
                            <?php
                            if ($row['homeStyle']==1) {
                                echo '<option value="1" selected>锋利</option>
                                      <option value="2">圆润</option>';
                            }else{
                                echo '<option value="1">锋利</option>
                                      <option value="2" selected>圆润</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                     <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">文章模块</label>
                        <select class="form-control" id="example-select" name="wzof">
                            <?php
                            if ($row['wzof']==1) {
                                echo '<option value="1" selected>打开</option>
                                      <option value="2">关闭</option>';
                            }else{
                                echo '<option value="1">打开</option>
                                      <option value="2" selected>关闭</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                     <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">帖子模块</label>
                        <select class="form-control" id="example-select" name="tzof">
                            <?php
                            if ($row['tzof']==1) {
                                echo '<option value="1" selected>打开</option>
                                      <option value="2">关闭</option>';
                            }else{
                                echo '<option value="1">打开</option>
                                      <option value="2" selected>关闭</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">文帖权重（文章帖子共存时，显示权重）</label>
                        <select class="form-control" id="example-select" name="modOrder">
                            <?php
                            if ($row['modOrder']==1) {
                                echo '<option value="1" selected>帖子优先</option>
                                      <option value="2">文章优先</option>';
                            }else{
                                echo '<option value="1">帖子优先</option>
                                      <option value="2" selected>文章优先</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                     <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">商城模块</label>
                        <select class="form-control" id="example-select" name="shopof">
                            <?php
                            if ($row['shopof']==1) {
                                echo '<option value="1" selected>打开</option>
                                      <option value="2">关闭</option>';
                            }else{
                                echo '<option value="1">打开</option>
                                      <option value="2" selected>关闭</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    <!--<div class="form-group mb-3">-->
                    <!--<script>-->
                    <!--    function myOnClickHandler42(obj) {-->
                    <!--        var input = document.getElementById("switch41");-->
                    <!--        console.log(input);-->
                    <!--        if (obj.checked) {-->
                    <!--            console.log("分享打开");-->
                    <!--            input.value = "1";-->
                    <!--        } else {-->
                    <!--            console.log("分享关闭");-->
                    <!--            input.value = "0";-->
                    <!--        }-->
                    <!--    }-->
                    <!--</script>-->
                    <!--<label for="validationCustom01">登录后可见</label>-->
                    <?php
                    //if ($row2['isLogin']==1) {
                    //    echo '<input type="checkbox" name="Lvof" id="switch41" value="1" data-switch="success"
                    //       onclick="myOnClickHandler42(this)" checked>';
                   // }else{
                    //   echo '<input type="checkbox" name="Lvof" id="switch41" value="0" data-switch="success"
                    //       onclick="myOnClickHandler42(this)">';
                    //}
                    ?>
                    <!--<label id="switchurl" style="display:block;" for="switch41" data-on-label="打开"-->
                    <!--       data-off-label="关闭"></label>-->
                    <!--</div>-->
                    <div class="form-group mb-3">
                        <script>
                            function isLogin1(obj) {
                                var input = document.getElementById("isLogin");
                                console.log(input);
                                if (obj.checked) {
                                    input.value = "1";
                                } else {
                                    input.value = "0";
                                }
                            }
                        </script>
                        <label for="validationCustom01">整站登录可见</label>
                            <?php
                            if ($isLogin=='1') {
                                echo '<input type="checkbox" name="isLogin" id="isLogin" value="1" data-switch="success"
                               onclick="isLogin1(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="isLogin" id="isLogin" value="0" data-switch="success"
                               onclick="isLogin1(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="isLogin" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler2(obj) {
                            var input = document.getElementById("switch1");
                            console.log(input);
                            if (obj.checked) {
                                console.log("分享打开");
                                input.value = "1";
                            } else {
                                console.log("分享关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">分享开关</label>
                    <?php
                    if ($row['Share']==1) {
                        echo '<input type="checkbox" name="Share" id="switch1" value="1" data-switch="success"
                           onclick="myOnClickHandler2(this)" checked>';
                    }else{
                        echo '<input type="checkbox" name="Share" id="switch1" value="0" data-switch="success"
                           onclick="myOnClickHandler2(this)">';
                    }
                    ?>
                    <label id="switchurl" style="display:block;" for="switch1" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler(obj) {
                                var input = document.getElementById("switch2");
                                var Tippingstyle = document.getElementById("Tippingstyle");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("打赏打开");
                                    input.value = "1";
                                    Tippingstyle.style.display = "block";
                                } else {
                                    console.log("打赏关闭");
                                    input.value = "0";
                                    Tippingstyle.style.display = "none";
                                }
                            }
                        </script>
                        <label for="validationCustom01">打赏开关</label>
                        <?php
                        if ($row['Tipping']==1) {
                            echo '<input type="checkbox" name="Tipping" id="switch2" value="1" data-switch="success"
                               onclick="myOnClickHandler(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="Tipping" id="switch2" value="0" data-switch="success"
                               onclick="myOnClickHandler(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="switch2" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">打赏用户展示</label>
                        <select class="form-control" id="example-select" name="Tippingstyle">
                            <?php
                            if ($row['Tippingstyle']==1) {
                                echo '<option value="1" selected>展示</option>
                                      <option value="2">隐藏</option>';
                            }else{
                                echo '<option value="1">展示</option>
                                      <option value="2" selected>隐藏</option>';
                            }
                            ?>
                            
                        </select>
                    </div>

                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler3(obj) {
                                var input = document.getElementById("switch4");
                                var PaymentMethods = document.getElementById("PaymentMethods");
                                console.log(input);
                                if (obj.checked) {
                                    console.log("充值打开");
                                    input.value = "1";
                                    PaymentMethods.style.display = "block";
                                } else {
                                    console.log("充值关闭");
                                    input.value = "0";
                                    PaymentMethods.style.display = "none";
                                }
                            }
                        </script>
                        <label for="validationCustom01">充值开关</label>
                            <?php
                            if ($row['Payswith']==1) {
                                echo '<input type="checkbox" name="Payswith" id="switch4" value="1" data-switch="success"
                               onclick="myOnClickHandler3(this)" checked>';
                            }else{
                                echo '<input type="checkbox" name="Payswith" id="switch4" value="0" data-switch="success"
                               onclick="myOnClickHandler3(this)">';
                            }
                            ?>
                        
                        <label id="switchurl" style="display:block;" for="switch4" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <?php
                    if ($row['Payswith']==1) {
                        echo '<div id="PaymentMethods" style="display: block;">';
                    } else {
                        echo '<div id="PaymentMethods" style="display: none;">';
                    }
                    ?>
                    
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler4(obj) {
                            var input = document.getElementById("Pay1");
                            console.log(input);
                            if (obj.checked) {
                                console.log("支付宝打开");
                                input.value = "1";
                            } else {
                                console.log("支付宝关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">支付宝</label>
                    <?php
                        if ($row['Alipay']==1) {
                            echo '<input type="checkbox" name="Alipay" id="Pay1" value="1" data-switch="success"
                           onclick="myOnClickHandler4(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="Alipay" id="Pay1" value="0" data-switch="success"
                           onclick="myOnClickHandler4(this)">';
                        }
                    ?>
                    
                    <label id="switchurl" style="display:block;" for="Pay1" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler5(obj) {
                            var input = document.getElementById("Pay2");
                            console.log(input);
                            if (obj.checked) {
                                console.log("微信打开");
                                input.value = "1";
                            } else {
                                console.log("微信关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">微信支付</label>
                    <?php
                        if ($row['WePay']==1) {
                            echo '<input type="checkbox" name="WePay" id="Pay2" value="1" data-switch="success"
                           onclick="myOnClickHandler5(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="WePay" id="Pay2" value="0" data-switch="success"
                           onclick="myOnClickHandler5(this)">';
                        }
                    ?>
                    
                    <label id="switchurl" style="display:block;" for="Pay2" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler6(obj) {
                            var input = document.getElementById("Pay3");
                            console.log(input);
                            if (obj.checked) {
                                console.log("卡密打开");
                                input.value = "1";
                            } else {
                                console.log("卡密关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">卡密兑换</label>
                    <?php
                        if ($row['Cami']==1) {
                            echo '<input type="checkbox" name="Cami" id="Pay3" value="1" data-switch="success"
                           onclick="myOnClickHandler6(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="Cami" id="Pay3" value="0" data-switch="success"
                           onclick="myOnClickHandler6(this)">';
                        }
                    ?>
                    
                    <label id="switchurl" style="display:block;" for="Pay3" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3">
                    <script>
                        function myOnClickHandler44(obj) {
                            var input = document.getElementById("Pay4");
                            console.log(input);
                            if (obj.checked) {
                                console.log("易支付打开");
                                input.value = "1";
                            } else {
                                console.log("易支付关闭");
                                input.value = "0";
                            }
                        }
                    </script>
                    <label for="validationCustom01">易支付</label>
                    <?php
                        if ($row['Yipay']==1) {
                            echo '<input type="checkbox" name="Yipay" id="Pay4" value="1" data-switch="success"
                           onclick="myOnClickHandler44(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="Yipay" id="Pay4" value="0" data-switch="success"
                           onclick="myOnClickHandler44(this)">';
                        }
                    ?>
                    
                    <label id="switchurl" style="display:block;" for="Pay4" data-on-label="打开"
                           data-off-label="关闭"></label>
                    </div> 
                    </div>
                    <div class="form-group mb-3">
                          <label for="Assetname">货币名称</label>
                          <input name="Assetname" class="form-control" type="text" required="" id="Assetname" placeholder="货币名称" value="<?php echo $row['Assetname']; ?>">
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">提现权限</label>
                            <select class="form-control" id="example-select" name="Withdrawals">
                                <?php
                                if ($row['Withdrawals'] == 1) {
                                    echo '<option value="1" selected>管理+审核+VIP</option>';
                                } else {
                                    echo '<option value="1">管理+审核+VIP</option>';
                                }
                                if ($row['Withdrawals'] == 2) {
                                    echo '<option value="2" selected>管理+VIP</option>';
                                } else {
                                    echo '<option value="2">管理+VIP</option>';
                                }
                                if ($row['Withdrawals'] == 3) {
                                    echo '<option value="3" selected>管理+审核</option>';
                                } else {
                                    echo '<option value="3">管理+审核</option>';
                                }
                                if ($row['Withdrawals'] == 4) {
                                    echo '<option value="4" selected>所有用户</option>';
                                } else {
                                    echo '<option value="4">所有用户</option>';
                                }
                                if ($row['Withdrawals'] == 5) {
                                    echo '<option value="5" selected>关闭提现</option>';
                                } else {
                                    echo '<option value="5">关闭提现</option>';
                                }
                                ?>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                          <label for="Threshold">货币提现门槛</label>
                          <input name="Threshold" class="form-control" type="number" required="" id="Threshold" placeholder="单位：货币" value="<?php echo $row['Threshold']; ?>">
                    </div>
                    <div class="form-group col-sm-4">
                      <label for="Premium">提现手续费</label>
                      <div class="d-flex align-items-center">
                        <input name="Premium" class="form-control" type="number" required="" id="Premium" placeholder="0~100" style="flex: 1;" value="<?php echo $row['Premium']; ?>">
                        <span style="margin-left: 5px;">%</span>
                      </div>
                    </div>
                    <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler8(obj) {
                                var input = document.getElementById("QQlogin");
                                var QQgroup = document.getElementById("QQgroup")
                                console.log(input);
                                if (obj.checked) {
                                    console.log("QQ登录打开");
                                    input.setAttribute("value", "1");
                                } else {
                                    console.log("QQ登录关闭");
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">QQ登录/绑定 开关</label>
                        <?php
                        if ($row['Qlogin']==1) {
                            echo '<input type="checkbox" name="Qlogin" id="QQlogin" value="1" data-switch="success"
                               onclick="myOnClickHandler8(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="Qlogin" id="QQlogin" value="0" data-switch="success"
                               onclick="myOnClickHandler8(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="QQlogin" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                     <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler99(obj) {
                                var input = document.getElementById("WXlogin");
                                var QQgroup = document.getElementById("WXgroup")
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">微信登录/绑定 开关</label>
                        <?php
                        if ($row['wxlogin']==1) {
                            echo '<input type="checkbox" name="wxlogin" id="WXlogin" value="1" data-switch="success"
                               onclick="myOnClickHandler99(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="wxlogin" id="WXlogin" value="0" data-switch="success"
                               onclick="myOnClickHandler99(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="WXlogin" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                     <div class="form-group mb-3">
                        <script>
                            function myOnClickHandler98(obj) {
                                var input = document.getElementById("WBlogin");
                                var QQgroup = document.getElementById("WBgroup")
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">微博登录/绑定 开关</label>
                        <?php
                        if ($row['wblogin']==1) {
                            echo '<input type="checkbox" name="wblogin" id="WBlogin" value="1" data-switch="success"
                               onclick="myOnClickHandler98(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="wblogin" id="WBlogin" value="0" data-switch="success"
                               onclick="myOnClickHandler98(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="WBlogin" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
<!--                    <div class="form-group col-sm-4">-->
<!--                    -->
<!--                        <label for="QQgroup">第三方用户登录设置</label>-->
<!--                            <select class="form-control" id="QQgroup" name="Qgroup">-->
<!--                                --><?php
//                                if ($row['Qgroup'] == 'contributor') {
//                                    echo '<option value="contributor" selected>可直接发帖【推荐】</option>
//                                          <option value="visitor">绑定邮箱才可发帖</option>';
//                                } else {
//                                    echo '<option value="contributor">可直接发帖【推荐】</option>
//                                          <option value="visitor" selected>绑定邮箱才可发帖</option>';
//                                }
//                                ?>
<!--                                -->
<!--                            </select>-->
<!--                    </div>-->

                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="settingsPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function check() {
        let Assetname = document.getElementsByName('Assetname')[0].value.trim();
        let wzof = document.getElementsByName('wzof')[0].value.trim();
        let tzof = document.getElementsByName('tzof')[0].value.trim();
        let Threshold = document.getElementsByName('Threshold')[0].value.trim();
        let Premium = document.getElementsByName('Premium')[0].value.trim();
        if (Assetname.length == 0) {
            alert("货币名称不能为空");
            return false;
        } else if (Threshold.length == 0) {
            alert("提现门槛不能为空");
            return false;
        } else if (Premium.length == 0) {
            alert("提现手续费不能为空");
            return false;
        } else if (wzof==tzof&&tzof==2) {
            alert("文章、帖子模块至少需要打开一个");
            return false;
        }

    }

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>