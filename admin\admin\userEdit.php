<?php
session_start();
?>

<?php
include_once 'Nav.php';
include_once 'connect.php';
$uid = $_GET['uid'];
$ipres2 = mysqli_query($connect, "SELECT * FROM typecho_users WHERE uid = '$uid'");
$userdata = mysqli_fetch_array($ipres2);
?>
<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">用户编辑</h4>

                <form class="needs-validation" action="userEditPost.php" method="post" novalidate>
                    <div class="form-group mb-3">
                        <label for="validationCustom03">UID</label>
                        
                        <input id="validationCustom03" type="text" class="form-control"
                                value="<?php echo $uid ?>" name="uid" readonly>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom04">用户名</label>
                        
                        <input type="text" class="form-control"  placeholder="用户名"
                               name="name" value="<?php echo $userdata['name'] ?>" required>
                    </div>
                     <div class="form-group mb-3">
                        <label for="validationCustom04">用户昵称</label>
                        
                        <input type="text" class="form-control"  placeholder="用户昵称"
                               name="screenName" value="<?php echo $userdata['screenName'] ?>" >
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom04">用户积分 <a class="fabu" href="giftAsset.php?uid=<?php echo $uid ?>">
                        <button type="button" class="btn btn-success2 btn-sm btn-rounded right_10">
                            <i class="dripicons-upload"></i> 快捷充扣
                        </button>
                    </a></label>
                        <input type="number" class="form-control"  placeholder="用户积分"
                               name="assets" value="<?php echo $userdata['assets'] ?>" readonly>
                       
                        
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="validationCustom04">用户经验</label>
                        
                        <input type="number" class="form-control"  placeholder="用户积分"
                               name="experience" value="<?php echo $userdata['experience'] ?>" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom04">邮箱</label>
                        
                        <input type="email" class="form-control"  placeholder="邮箱"
                               name="mail" value="<?php echo $userdata['mail'] ?>" required>
                    </div>
                     <div class="form-group col-sm-4">
                        <label for="validationCustom01">性别</label>
                            <select class="form-control" id="example-select" name="url">
                       <?php
                                if ($userdata['url'] == 'man') {
                                    echo '<option value="man" selected>男</option>';
                                } else {
                                    echo '<option value="man">男</option>';
                                }
                                if ($userdata['url'] == 'woman') {
                                    echo '<option value="woman" selected>女</option>';
                                } else {
                                    echo '<option value="woman">女</option>';
                                }
                                if ($userdata['url'] == 'bm') {
                                    echo '<option value="bm" selected>不展示</option>';
                                } else {
                                    echo '<option value="bm">不展示</option>';
                                }
                                ?>
                        </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom04">头衔</label>
                        
                        <input type="customize" class="form-control"  placeholder="为用户自定义头衔"
                               name="customize" value="<?php echo $userdata['customize'] ?>" >
                    </div>
                      <div class="form-group col-sm-4">
                        <label for="validationCustom01">用户组</label>
                            <select class="form-control" id="example-select" name="group">
                       <?php
                                if ($userdata['group'] == 'administrator') {
                                    echo '<option value="administrator" selected>管理员</option>';
                                } else {
                                    echo '<option value="administrator">管理员</option>';
                                }
                                if ($userdata['group'] == 'editor') {
                                    echo '<option value="editor" selected>编辑</option>';
                                } else {
                                    echo '<option value="editor">编辑</option>';
                                }
                                if ($userdata['group'] == 'contributor') {
                                    echo '<option value="contributor" selected>贡献者</option>';
                                } else {
                                    echo '<option value="contributor">贡献者</option>';
                                }
                                if ($userdata['group'] == 'subscriber') {
                                    echo '<option value="subscriber" selected>关注者</option>';
                                } else {
                                    echo '<option value="subscriber">关注者</option>';
                                }
                                if ($userdata['group'] == 'visitor') {
                                    echo '<option value="visitor" selected>游客</option>';
                                } else {
                                    echo '<option value="visitor">游客</option>';
                                }
                                ?>
                        </select>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">是否重置密码</label> <span class="badge badge-success-lighten"style="font-size: 0.8rem;">重置后密码为123456</span>
                            <select class="form-control" id="example-select" name="repw">
                                <option value="false" selected>否</option>
                                <option value="true">是</option>
                            </select>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success"  type="submit" id="userEditPost">提交修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  


</div>


<?php
include_once 'Footer.php';
?>

</body>
</html>