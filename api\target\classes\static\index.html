<!doctype html>
<html data-n-head-ssr lang="en" data-n-head="%7B%22lang%22:%7B%22ssr%22:%22en%22%7D%7D">
  <head>
    <title>StarProApi - 为简约而生</title><meta data-n-head="ssr" charset="utf-8"><meta data-n-head="ssr" name="viewport" content="width=device-width,initial-scale=1"><meta data-n-head="ssr" data-hid="description" name="description" content=""><meta data-n-head="ssr" name="format-detection" content="telephone=no"><link data-n-head="ssr" rel="icon" type="image/x-icon" href="/favicon.ico"><link rel="preload" href="/style/953e89d.js" as="script"><link rel="preload" href="/style/b44b605.js" as="script"><link rel="preload" href="/style/css/4dc5d3b.css" as="style"><link rel="preload" href="/style/3696876.js" as="script"><link rel="preload" href="/style/css/521006d.css" as="style"><link rel="preload" href="/style/f2e8b74.js" as="script"><link rel="preload" href="/style/css/8e10895.css" as="style"><link rel="preload" href="/style/a675b4e.js" as="script"><link rel="stylesheet" href="/style/css/4dc5d3b.css"><link rel="stylesheet" href="/style/css/521006d.css"><link rel="stylesheet" href="/style/css/8e10895.css">
  </head>
  <body>
    <div data-server-rendered="true" id="__nuxt"><!----><div id="__layout"><div class="container"><div class="index-main"><div style="margin-top:70px"><span class="star-logo1 font50">StarProApi</span><span class="star-logo3">2.0</span></div> <div class="rule-version"><br> <div class="rule-btn"><div class="el-row"><span round="" class="star-button1">安装/更新</span> <span round="" class="star-button2">官方文档</span></div> <div class="el-row"><div class="protocol"><a href="https://www.yuque.com/senyun-ev0j3/starpro/esvckfg8h71cv7wu" target="_blank">许可协议 / 免责申明</a></div></div></div></div> <div class="el-dialog__wrapper" style="display:none"><div role="dialog" aria-modal="true" aria-label="进行第二步安装" class="el-dialog" style="margin-top:15vh;width:400px"><div class="el-dialog__header"><span class="el-dialog__title">进行第二步安装</span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><div class="el-dialog__footer"><span class="dialog-footer"><button type="button" class="el-button el-button--default is-round"><!----><!----><span>取 消</span></button> <button type="button" class="el-button el-button--primary is-round"><!----><!----><span>确 定</span></button> <!----> <!----> <!----></span></div></div></div> <div class="el-dialog__wrapper" style="display:none"><div role="dialog" aria-modal="true" aria-label="开始第一步安装" class="el-dialog" style="margin-top:15vh;width:400px"><div class="el-dialog__header"><span class="el-dialog__title">开始第一步安装</span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><div class="el-dialog__footer"><span class="dialog-footer"><button type="button" class="el-button el-button--danger is-round"><!----><!----><span>开始安装</span></button> <button type="button" class="el-button el-button--primary is-round"><!----><!----><span>仅更新</span></button> <button type="button" class="el-button el-button--blue is-round"><!----><!----><span>再等等</span></button> <!----></span></div></div></div></div></div></div></div><script>window.__NUXT__={layout:"no",data:[{}],fetch:{},error:null,serverRendered:!0,routePath:"/",config:{_app:{basePath:"/",assetsPath:"/style/",cdnURL:null}}}</script><script src="/style/953e89d.js" defer></script><script src="/style/a675b4e.js" defer></script><script src="/style/b44b605.js" defer></script><script src="/style/3696876.js" defer></script><script src="/style/f2e8b74.js" defer></script>
  </body>
</html>
