<?php
session_start();
?>



<?php
include_once 'Nav.php';
$id = $_GET['id'];
$withdrawals = "SELECT * FROM typecho_forum_section WHERE type = 'section' ORDER BY id DESC";
$withdrawalsResult = mysqli_query($connect, $withdrawals);
$article = "SELECT * FROM typecho_forum WHERE id='$id' limit 1";
$resarticle = mysqli_query($connect, $article);
$mod = mysqli_fetch_array($resarticle);
$section = $mod['section'];
$article2 = "SELECT * FROM typecho_forum_section WHERE id='$section' limit 1";
$resarticle2 = mysqli_query($connect, $article2);
$mod2 = mysqli_fetch_array($resarticle2);
?>


<div class="row">

    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3 size_18">审核帖子</h4>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">帖子id</label>
                        <input type="text" class="form-control" id="validationCustom01"
                               name="id" value="<?php echo $id ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">发布者UID</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入发布者UID"
                               name="uid" value="<?php echo $mod['authorId'] ?>" readonly>
                    </div>
                    <div class="form-group col-sm-4">
                        <label for="validationCustom01">圈子</label>
                            <select class="form-control" id="example-select" name="category" readonly>
                                    <option value="<?php echo $mod2['id']; ?>" selected><?php echo $mod2['name']; ?></option>
                            </select>
                    </div>
                    <div class="form-group mb-3">
                        <label for="validationCustom01">帖子标题</label>
                        <input type="text" class="form-control" id="validationCustom01" placeholder="请输入标题"
                               name="articletitle" value="<?php echo $mod['title'] ?>" readonly>
                    </div>
                    <label for="validationCustom01">帖子内容</label><span class="badge badge-success-lighten"style="font-size: 0.8rem;">审核时不支持修改</span>
                    <div id="editor—wrapper">
                            <div id="editor-container"></div>
                             <textarea id="editorContent" name="articletext" style="display: none;"><?php echo $mod['text'] ?></textarea>  
                        </div>
                    <div class="form-group mb-3 text_right">
                        <?php if($mod['isrecommend']=='0'){?>
                            <a class="fabu" href="forumAuditPost.php?id=<?php echo $id ?>&status=isrecommend">
                            <button class="btn btn-info btn-rounded" id="payoutPost" style="margin-right:10px">加精</button>
                            </a>
                        <?php } else { ?>
                        <a class="fabu" href="forumAuditPost.php?id=<?php echo $id ?>&status=unrecommend">
                            <button class="btn btn-danger" id="payoutPost" style="margin-right:10px">取消加精</button>
                        </a>
                        <?php } ?>
                        <?php if($mod['isswiper']=='0'){?>
                                <a class="fabu" href="forumAuditPost.php?id=<?php echo $id ?>&status=isswiper">
                            <button class="btn btn-info btn-rounded" id="payoutPost" style="margin-right:10px">轮播</button>
                            </a>
                        <?php } else { ?>
                                <a class="fabu" href="forumAuditPost.php?id=<?php echo $id ?>&status=unswiper">
                            <button class="btn btn-danger" id="payoutPost" style="margin-right:10px">取消轮播</button>
                        </a>
                        <?php } ?>
                        <?php if($mod['isTop']=='0'){?>
                            <a class="fabu" href="forumAuditPost.php?id=<?php echo $id ?>&status=istop">
                            <button class="btn btn-info btn-rounded" id="payoutPost" style="margin-right:10px">置顶</button>
                            </a>
                        <?php } else { ?>
                         <a class="fabu" href="forumAuditPost.php?id=<?php echo $id ?>&status=untop">
                            <button class="btn btn-danger" id="payoutPost" style="margin-right:10px">取消置顶</button>
                        </a>
                        <?php } ?>
                        <?php if($mod['status']=='0'){?>
                        <a class="fabu" onclick="Pass('<?php echo $id ;?>')">
                            <button class="btn btn-primary" id="payoutPost" style="margin-right:10px">通过</button>
                        </a>
                        <a class="fabu" onclick="Refuse('<?php echo $id ;?>')">
                            <button class="btn btn-danger" id="payoutPost">驳回</button>
                        </a>
                        <?php } else if($mod['status']=='1') { ?>
                        <a class="fabu" onclick="Lock('<?php echo $id ;?>')">
                            <button class="btn btn-info btn-rounded" id="payoutPost" style="margin-right:10px">锁定</button>
                        </a>
                        <a class="fabu" onclick="Refuse('<?php echo $id ;?>')">
                            <button class="btn btn-danger" id="payoutPost">驳回</button>
                        </a>
                        <?php }else{ ?>
                        <a class="fabu" onclick="unLock('<?php echo $id ;?>')">
                            <button class="btn btn-danger" id="payoutPost">解锁</button>
                        </a>
                        <?php } ?>
                    </div>


            </div> <!-- end card-body-->
        </div> <!-- end card-->
    </div> <!-- end col-->
</div>

<link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
<script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js"></script>
<script>
const { createEditor, createToolbar } = window.wangEditor
const editorConfig = {
    placeholder: '请输入内容...',
    onChange(editor) {
      const html = editor.getHtml();  
    document.getElementById('editorContent').value = html;  
    },  
}
<?php 
$edithOf = true; 
include_once 'editor.php';
?>
<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function Pass(id) {
        if (confirm('您确认要通过该帖子吗？')) {
            location.href = 'forumAuditPost.php?id=' + id +'&status=Pass';
        }
    }
    function Refuse(id) {
        if (confirm('您确认要拒绝该帖子吗？')) {
            location.href = 'forumAuditPost.php?id=' + id +'&status=Refuse';
        }
    }
    function Lock(id) {
        if (confirm('您确认要锁定该帖子吗？')) {
            location.href = 'forumAuditPost.php?id=' + id +'&status=Lock';
        }
    }
    function unLock(id) {
        if (confirm('您确认要解锁该帖子吗？')) {
            location.href = 'forumAuditPost.php?id=' + id +'&status=unLock';
        }
    }
</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>