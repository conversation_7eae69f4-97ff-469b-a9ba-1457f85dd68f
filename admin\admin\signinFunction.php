<?php
session_start();
?>



<?php
include_once 'Nav.php';
$sql = "SELECT * FROM Sy_functions";
$result = mysqli_query($connect, $sql);
// 检查查询结果是否为空
if (mysqli_num_rows($result) > 0) {
    // 获取第一行数据作为结果集
    $row = mysqli_fetch_assoc($result);
}

$sql2 = "SELECT * FROM Sy_set";
$result2 = mysqli_query($connect, $sql2);
if (mysqli_num_rows($result2) > 0) {
    $row2 = mysqli_fetch_assoc($result2);
}
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">签到设置</h4>

                <form class="needs-validation" action="signinFunctionPost.php" method="post" onsubmit="return check()"
                      novalidate>
                    <div class="form-group col-sm-4" style="width:85%">
                      <label for="Vipdiscount">单次签到奖励：</label>
                      <div class="d-flex align-items-center">
                        <input name="Signinexp1" class="form-control" type="number" required="" id="Vipdiscount" placeholder="单次签到奖励" style="flex: 1;" value="<?php echo $row['Signinexp1']; ?>">
                        <span style="margin-left: 15px;">经验</span>
                      </div>
                      <div class="d-flex align-items-center margin-t5">
                            <input name="Signinasset1" class="form-control" type="number" required="" id="Vipdiscount" placeholder="单次签到奖励" style="flex: 1;" value="<?php echo $row['Signinasset1']; ?>">
                            <span style="margin-left: 15px;"><?php echo $row2['Assetname']; ?></span>
                        </div>
                    </div>
                    <div class="form-group col-sm-4" style="width:85%">
                      <label for="Vipdiscount">连续签到2天：</label>
                      <div class="d-flex align-items-center">
                        <input name="Signinexp2" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinexp2']; ?>">
                        <span style="margin-left: 15px;">经验</span>
                      </div>
                      <div class="d-flex align-items-center margin-t5">
                            <input name="Signinasset2" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinasset2']; ?>">
                            <span style="margin-left: 15px;"><?php echo $row2['Assetname']; ?></span>
                        </div>
                    </div>
                    <div class="form-group col-sm-4" style="width:85%">
                      <label for="Vipdiscount">连续签到3天：</label>
                      <div class="d-flex align-items-center">
                        <input name="Signinexp3" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinexp3']; ?>">
                        <span style="margin-left: 15px;">经验</span>
                      </div>
                      <div class="d-flex align-items-center margin-t5">
                            <input name="Signinasset3" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinasset3']; ?>">
                            <span style="margin-left: 15px;"><?php echo $row2['Assetname']; ?></span>
                        </div>
                    </div>
                    <div class="form-group col-sm-4" style="width:85%">
                      <label for="Vipdiscount">连续签到4天：</label>
                      <div class="d-flex align-items-center">
                        <input name="Signinexp4" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinexp4']; ?>">
                        <span style="margin-left: 15px;">经验</span>
                      </div>
                      <div class="d-flex align-items-center margin-t5">
                            <input name="Signinasset4" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinasset4']; ?>">
                            <span style="margin-left: 15px;"><?php echo $row2['Assetname']; ?></span>
                        </div>
                    </div>
                    <div class="form-group col-sm-4" style="width:85%">
                      <label for="Vipdiscount">连续签到5天：</label>
                      <div class="d-flex align-items-center">
                        <input name="Signinexp5" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinexp5']; ?>">
                        <span style="margin-left: 15px;">经验</span>
                      </div>
                      <div class="d-flex align-items-center margin-t5">
                            <input name="Signinasset5" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinasset5']; ?>">
                            <span style="margin-left: 15px;"><?php echo $row2['Assetname']; ?></span>
                        </div>
                    </div>
                    <div class="form-group col-sm-4" style="width:85%">
                      <label for="Vipdiscount">连续签到6天：</label>
                      <div class="d-flex align-items-center">
                        <input name="Signinexp6" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinexp6']; ?>">
                        <span style="margin-left: 15px;">经验</span>
                      </div>
                      <div class="d-flex align-items-center margin-t5">
                            <input name="Signinasset6" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinasset6']; ?>">
                            <span style="margin-left: 15px;"><?php echo $row2['Assetname']; ?></span>
                        </div>
                    </div>
                    <div class="form-group col-sm-4" style="width:85%">
                      <label for="Vipdiscount">连续签到7天：</label>
                      <div class="d-flex align-items-center">
                        <input name="Signinexp7" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinexp7']; ?>">
                        <span style="margin-left: 15px;">经验</span>
                      </div>
                      <div class="d-flex align-items-center margin-t5">
                            <input name="Signinasset7" class="form-control" type="number" required="" id="Vipdiscount" placeholder="连续签到奖励" style="flex: 1;" value="<?php echo $row['Signinasset7']; ?>">
                            <span style="margin-left: 15px;"><?php echo $row2['Assetname']; ?></span>
                        </div>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="signinFunctionPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->


<script>
    function check() {
        let Assetname = document.getElementsByName('Assetname')[0].value.trim();
        let Threshold = document.getElementsByName('Threshold')[0].value.trim();
        let Premium = document.getElementsByName('Premium')[0].value.trim();
        if (Assetname.length == 0) {
            alert("货币名称不能为空");
            return false;
        } else if (Threshold.length == 0) {
            alert("提现门槛不能为空");
            return false;
        } else if (Premium.length == 0) {
            alert("提现手续费不能为空");
            return false;
        }

    }

</script>

<?php
include_once 'Footer.php';
?>

</body>
</html>