<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.consumerDao">
<resultMap id="BaseResultMap" type="com.StarProApi.entity.identifyConsumer" >
    <result column="uid" property="uid" />
    <result column="idCard" property="idCard" />
    <result column="name" property="name" />
    <result column="identifyStatus" property="identifyStatus" />
</resultMap>
    <sql id="base-field">
        tcs.uid,
        tcs.idCard,
        tcs.name,
        tcs.identifyStatus
    </sql>
    <sql id="Base_Column_List">
        `uid`,
        `idCard`,
        `name`,
        `identifyStatus`
    </sql>
    <update id="update" parameterType="com.StarProApi.entity.identifyConsumer">
        update ${prefix}_consumer
        <set>
            <if test="null!=idCard">idCard = #{idCard},</if>
            <if test="null!=name">name = #{name},</if>
            <if test="null!=identifyStatus">identifyStatus = #{identifyStatus}</if>
        </set>
        <where>uid=#{uid}</where>
    </update>

    <delete id="remove" parameterType="string">
        delete from ${prefix}_consumer
        where uid = #{key}
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_consumer
        WHERE `uid` = #{uid}
    </select>

    <select id="queryInfo" resultMap="BaseResultMap">
        select <include refid="base-field"/>
        from ${prefix}_consumer tcs
        <where>
            <if test="identifyConsumer.uid != null">
                and tcs.uid = #{identifyConsumer.uid}
            </if>
            <if test="identifyConsumer.idCard != null">
                and tcs.idCard = #{identifyConsumer.idCard}
            </if>
            <if test="identifyConsumer.name != null">
                and tcs.name = #{identifyConsumer.name}
            </if>
            <if test="identifyConsumer.identifyStatus != null">
                and tcs.identifyStatus = #{identifyConsumer.identifyStatus}
            </if>
        </where>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <insert id="insert" parameterType="com.StarProApi.entity.identifyConsumer">
        insert into ${prefix}_consumer(uid,idCard,name,identifyStatus)
        values (#{uid},#{idCard},#{name},'0')
    </insert>
</mapper>