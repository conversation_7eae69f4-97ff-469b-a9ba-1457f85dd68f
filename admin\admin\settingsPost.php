<?php
session_start();
$file = $_SERVER['PHP_SELF'];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    include_once 'connect.php';
    $Lvof = isset($_POST['Lvof']) ? $_POST['Lvof'] : 0;
    $Auditurl = $_POST['Auditurl'];
    $Waiterurl = $_POST['Waiterurl'];
    $Groupurl = $_POST['Groupurl'];
    $Share = isset($_POST['Share']) ? $_POST['Share'] : 0;
    $Tipping = isset($_POST['Tipping']) ? $_POST['Tipping'] : 0;
    $Payswith = isset($_POST['Payswith']) ? $_POST['Payswith'] : 0;
    $Alipay = isset($_POST['Alipay']) ? $_POST['Alipay'] : 0;
    $WePay = isset($_POST['WePay']) ? $_POST['WePay'] : 0;
    $Cami = isset($_POST['Cami']) ? $_POST['Cami'] : 0;
    $Yipay = isset($_POST['Yipay']) ? $_POST['Yipay'] : 0;
    $Tippingstyle = $_POST['Tippingstyle'];
    $Assetname = $_POST['Assetname'];
    $Withdrawals = $_POST['Withdrawals'];
    $Threshold = $_POST['Threshold'];
    $Premium = $_POST['Premium'];
    $Qlogin = isset($_POST['Qlogin']) ? $_POST['Qlogin'] : 0;
    $Qgroup = $_POST['Qgroup'];
    $h5of = isset($_POST['h5of']) ? $_POST['h5of'] : 0;
    $wxlogin = isset($_POST['wxlogin']) ? $_POST['wxlogin'] : 0;
    $wblogin = isset($_POST['wblogin']) ? $_POST['wblogin'] : 0;
    $homeStyle = $_POST['homeStyle'];
    $wzof = $_POST['wzof'];
    $tzof = $_POST['tzof'];
    $modOrder = $_POST['modOrder'];
    $shopof = $_POST['shopof'];
    
    //api
    $postMax = $_POST['postMax'];
    $webinfoTitle = $_POST['webinfoTitle'];
    $isLogin = isset($_POST['isLogin']) && $_POST['isLogin'] == 1 ? true : false;  
        
    
    if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
    $redisKeys = $connectRedis->keys('starapi_*');
        foreach ($redisKeys as $redisKey) {
            $connectRedis->del($redisKey);
        }
        // 写入数据库
        $query = "UPDATE Sy_set SET Auditurl=?, Waiterurl=?, Groupurl=?, Share=?, Tipping=?, Payswith=?, Alipay=?, WePay=?, Cami=?, Yipay=?, Tippingstyle=?, Assetname=?, Withdrawals=?, Threshold=?, Premium=?, Qlogin=?, Qgroup=?, h5of=?, wxlogin=?, wblogin=?, wzof=?, tzof=?, modOrder=?, shopof=?, homeStyle=?";
        $stmt = $connect->prepare($query);
        // $query2 = "UPDATE `typecho_apiconfig` SET `isLogin`=?";
        // $stmt2 = $connect->prepare($query2);
        
        //api
        $curl = curl_init();
        $api_url = $API_CONFIG_UPDATE;
        $params = array(  
        'postMax' => $postMax,
        'isLogin' => $isLogin,
        'webinfoTitle' => $webinfoTitle
        );  
        $encoded_params = urlencode(json_encode($params)); 
        $url = $api_url . '?' . 'params=' . $encoded_params . '&webkey=' . $api_key;   
        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $responseData = json_decode($response, true);

        $redisKeys = $connectRedis->keys('starapi_*');
        foreach ($redisKeys as $redisKey) {
            $connectRedis->del($redisKey);
        }
        if ($stmt) {
            $stmt->bind_param("sssiiiiiiisssssisiiisssss",  $Auditurl, $Waiterurl, $Groupurl, $Share, $Tipping, $Payswith, $Alipay, $WePay, $Cami, $Yipay, $Tippingstyle, $Assetname, $Withdrawals, $Threshold, $Premium, $Qlogin, $Qgroup, $h5of, $wxlogin, $wblogin, $wzof, $tzof, $modOrder, $shopof, $homeStyle);
            // $stmt2->bind_param("i", $Lvof);location.href = 'settings.php';
            if ($stmt->execute()&&$responseData['code'] == 1) {
                echo "<script>alert('更改成功');location.href = 'settings.php';</script>";
            } else {
                echo "<script>alert('更改失败,error:api=>".$responseData['msg']."');</script>";
            }
            $stmt->close();
        } else {
            echo "<script>alert('无法连接数据库');location.href = 'settings.php';</script>";
        }
    } else {
        echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
    }
} else {
    
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
?>
