<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoViolationDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoViolation" >
        <result column="id" property="id" />
        <result column="uid" property="uid" />
        <result column="type" property="type" />
        <result column="text" property="text" />
        <result column="created" property="created" />
        <result column="handler" property="handler" />
        <result column="value" property="value" />

    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `uid`,
        `type`,
        `text`,
        `created`,
        `handler`,
        `value`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoViolation">
        INSERT INTO ${prefix}_violation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != type'>
                `type`,
            </if>
            <if test ='null != text'>
                `text`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != handler'>
                `handler`,
            </if>
            <if test ='null != value'>
                `value`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != type'>
                #{type},
            </if>
            <if test ='null != text'>
                #{text},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != handler'>
                #{handler},
            </if>
            <if test ='null != value'>
                #{value}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_violation ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.uid},
                #{curr.type},
                #{curr.text},
                #{curr.created},
                #{curr.handler}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoViolation">
        UPDATE ${prefix}_violation
        <set>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != type'>`type` = #{type},</if>
            <if test ='null != text'>`text` = #{text},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != handler'>`handler` = #{handler},</if>
            <if test ='null != value'>`value` = #{value}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_violation
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_violation WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_violation
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_violation
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != handler'>
                and `handler` = #{handler}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_violation
        <where>
            <if test ='null != typechoViolation.id'>
                and `id` = #{typechoViolation.id}
            </if>
            <if test ='null != typechoViolation.uid'>
                and `uid` = #{typechoViolation.uid}
            </if>
            <if test ='null != typechoViolation.type'>
                and `type` = #{typechoViolation.type}
            </if>
            <if test ='null != typechoViolation.text'>
                and `text` = #{typechoViolation.text}
            </if>
            <if test ='null != typechoViolation.created'>
                and `created` = #{typechoViolation.created}
            </if>
            <if test ='null != typechoViolation.handler'>
                and `handler` = #{typechoViolation.handler}
            </if>
            <if test ='null != typechoViolation.value'>
                and `value` = #{typechoViolation.valuer}
            </if>

        </where>
        order by created desc
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_violation
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != handler'>
                and `handler` = #{handler}
            </if>
        </where>
    </select>
</mapper>