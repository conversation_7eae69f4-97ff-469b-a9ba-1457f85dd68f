<?php
session_start();
?>
<?php
include_once 'connect.php';
$id = $_POST['id'];
$url = $_POST['url'];
$name = $_POST['name'];
$link = $_POST['link'];
$lgof = $_POST['lgof'];
$file = $_SERVER['PHP_SELF'];


if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $charu = "UPDATE Sy_icon SET `url`='$url' , `name`='$name' ,  `link`='$link' , `lgof`='$lgof'  WHERE id = '$id'";
    $result = mysqli_query($connect, $charu);
    if ($result) {
            echo "<script>alert('修改成功');location.href = 'iconAdmin.php';</script>";
        } else {
            echo "<script>alert('修改失败');history.back();</script>";   
        }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}
