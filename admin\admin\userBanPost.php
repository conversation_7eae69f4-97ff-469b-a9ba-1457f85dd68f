<?php
session_start();
?>

<?php
$banuid = $_POST['banuid'];
$bantext = $_POST['bantext'];
$bantime = strtotime($_POST['bantime']);
$time = gmdate("Y-m-d H:i:s", time() + 8 * 3600);
$nowtime = strtotime($time);
$file = $_SERVER['PHP_SELF'];

include_once 'connect.php';

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $updateQuery = "UPDATE typecho_users SET bantime = ? WHERE uid = ?";
    $updateStmt = mysqli_prepare($connect, $updateQuery);
    mysqli_stmt_bind_param($updateStmt, "ss", $bantime, $banuid);
    $updateResult = mysqli_stmt_execute($updateStmt);

    if (!$updateResult) {
        echo mysqli_stmt_error($updateStmt).$nowtime;
    } else {
        $insertQuery = "INSERT INTO typecho_violation (uid, type, text, created, handler) VALUES (?, 'manager', ?, ?, '0')";
        $insertStmt = mysqli_prepare($connect, $insertQuery);
        mysqli_stmt_bind_param($insertStmt, "sss", $banuid, $bantext, $nowtime);
        $insertResult = mysqli_stmt_execute($insertStmt);

        if (!$insertResult) {
            echo mysqli_stmt_error($insertStmt).$nowtime;
        } else {
            echo "<script>alert('封禁成功');location.href = 'userAdmin.php';</script>";
        }
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}