<?php
session_start();
?>

<?php

$number = intval($_POST['code']);
$price = intval($_POST['price']);
$time = gmdate("Y-m-d H:i:s", time() + 8 * 3600);
$file = $_SERVER['PHP_SELF'];
include_once 'connect.php';
function generateRandomString($length) {
    $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    $string = '';
    for ($i = 0; $i < $length; $i++) {
        $randomIndex = mt_rand(0, strlen($characters) - 1);
        $string .= $characters[$randomIndex];
        
        if ($i == 7 || $i == 11 || $i == 15) {
            $string .= '-';
        }
    }
    return $string;
}

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    for ($j = 1; $j <= $number; $j++) {
        $cardKey = generateRandomString(32);
        $created = time();
    
        $sql = "INSERT INTO typecho_paykey (value, price, created, status, uid) VALUES ('$cardKey', '$price', '$created', '0', '-1')";
    
        if ($connect->query($sql) === TRUE) {
             echo "<script>alert('生成成功');location.href = 'kmAdmin.php';</script>";
        } else {
            echo "<script>alert('生成失败');location.href = 'kmAdmin.php';</script>";   
        }
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}

