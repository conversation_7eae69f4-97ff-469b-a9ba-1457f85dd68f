<?php
session_start();
?>



<?php
include_once 'Nav.php';

//获取配置有问题
$curl = curl_init();
$url = $API_GET_API_CONFIG.'?webkey='.$api_key;
curl_setopt_array($curl, array(
   CURLOPT_URL => $url,
   CURLOPT_RETURNTRANSFER => true,
   CURLOPT_ENCODING => '',
   CURLOPT_MAXREDIRS => 10,
   CURLOPT_TIMEOUT => 0,
   CURLOPT_SSL_VERIFYPEER => false,
   CURLOPT_SSL_VERIFYHOST => false,
   CURLOPT_FOLLOWLOCATION => true,
   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
   CURLOPT_CUSTOMREQUEST => 'GET',
));

$response = curl_exec($curl);
$responseData = json_decode($response, true);  
  
if ($responseData && isset($responseData['code']) && $responseData['code'] == 1) {  
    $forumAudit = $responseData['data']['forumAudit'];    
    $forumReplyAudit = $responseData['data']['forumReplyAudit'];  
    $spaceAudit = $responseData['data']['spaceAudit'];  
    $auditlevel = $responseData['data']['auditlevel'];  
    $contentAuditlevel = $responseData['data']['contentAuditlevel'];  
    $forbidden = $responseData['data']['forbidden'];
    $fields = $responseData['data']['fields'];  
    $disableCode = $responseData['data']['disableCode'];  
    $allowDelete = $responseData['data']['allowDelete'];  
    $identifylvPost = $responseData['data']['identifylvPost'];  
    $identifysmPost = $responseData['data']['identifysmPost']; 

} 
?>
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <h4 class="header-title mb-3">审核设置</h4>

                <form class="needs-validation" action="setAuditPost.php" method="post"
                      novalidate>
                     <div class="form-group col-sm-4">
                        <script>
                            function myOnClickHandler0(obj) {
                                var input = document.getElementById("identifylvPost");
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">只允许蓝V用户发布</label>
                        <?php
                        if ($identifylvPost=='1') {
                            echo '<input type="checkbox" name="identifylvPost" id="identifylvPost" value="1" data-switch="success"
                               onclick="myOnClickHandler0(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="identifylvPost" id="identifylvPost" value="0" data-switch="success"
                               onclick="myOnClickHandler0(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="identifylvPost" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4">
                        <script>
                            function myOnClickHandler5(obj) {
                                var input = document.getElementById("identifysmPost");
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">只允许实名用户发布</label>
                        <?php
                        if ($identifysmPost=='1') {
                            echo '<input type="checkbox" name="identifysmPost" id="identifysmPost" value="1" data-switch="success"
                               onclick="myOnClickHandler5(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="identifysmPost" id="identifysmPost" value="0" data-switch="success"
                               onclick="myOnClickHandler5(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="identifysmPost" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                     <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">文章审核</label>
                        <select class="form-control" id="example-select" name="contentAuditlevel">
                            <?php
                            if ($contentAuditlevel==0) {
                                echo '<option value="0" selected>直接发布</option>
                                      <option value="1">违禁词匹配审核</option>
                                      <option value="2">默认全部审核</option>';
                            }elseif ($contentAuditlevel==1){
                                echo '<option value="0" >直接发布</option>
                                      <option value="1" selected>违禁词匹配审核</option>
                                      <option value="2">默认全部审核</option>';
                            }elseif ($contentAuditlevel==2){
                                echo '<option value="0" >直接发布</option>
                                      <option value="1">违禁词匹配审核</option>
                                      <option value="2" selected>默认全部审核</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                    <div class="form-group col-sm-4">
                    
                        <label for="validationCustom01">文章评论审核</label>
                        <select class="form-control" id="example-select" name="auditlevel">
                            <?php
                            if ($auditlevel==0) {
                                echo '<option value="0" selected>直接发布</option>
                                      <option value="1" >第一次评论审核</option>
                                      <option value="2" >违禁词匹配审核</option>
                                      <option value="3" >违禁词匹配拦截</option>
                                      <option value="4" >默认全部审核</option>';
                            }elseif ($auditlevel==1){
                               echo '<option value="0" >直接发布</option>
                                      <option value="1" selected>第一次评论审核</option>
                                      <option value="2" >违禁词匹配审核</option>
                                      <option value="3" >违禁词匹配拦截</option>
                                      <option value="4" >默认全部审核</option>';
                            }elseif ($auditlevel==2){
                                echo '<option value="0" >直接发布</option>
                                      <option value="1" >第一次评论审核</option>
                                      <option value="2" selected>违禁词匹配审核</option>
                                      <option value="3" >违禁词匹配拦截</option>
                                      <option value="4" >默认全部审核</option>';
                            }elseif ($auditlevel==3){
                                echo '<option value="0" >直接发布</option>
                                      <option value="1" >第一次评论审核</option>
                                      <option value="2" >违禁词匹配审核</option>
                                      <option value="3" selected>违禁词匹配拦截</option>
                                      <option value="4" >默认全部审核</option>';
                            }elseif ($auditlevel==4){
                                echo '<option value="0" >直接发布</option>
                                      <option value="1" >第一次评论审核</option>
                                      <option value="2" >违禁词匹配审核</option>
                                      <option value="3" >违禁词匹配拦截</option>
                                      <option value="4" selected>默认全部审核</option>';
                            }
                            ?>
                            
                        </select>
                    </div>
                     <div class="form-group mb-3">
                      <label for="notice">违禁词
                          <span style="font-size: 0.7rem;color:#acacac;margin-left:10px">
                              用于文章、文章评论、个性签名审核拦截。根据英文逗号”,“进行分割，不要存在换行或者空格。
                          </span></label>
                      <textarea id="notice" class="form-control" placeholder="请输入违禁词（可留空）" name="forbidden" rows="3"><?php echo $forbidden ?></textarea>
                    </div>
                     <div class="form-group col-sm-4">
                        <script>
                            function myOnClickHandler1(obj) {
                                var input = document.getElementById("forumAudit");
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">帖子审核</label>
                        <?php
                        if ($forumAudit=='1') {
                            echo '<input type="checkbox" name="forumAudit" id="forumAudit" value="1" data-switch="success"
                               onclick="myOnClickHandler1(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="forumAudit" id="forumAudit" value="0" data-switch="success"
                               onclick="myOnClickHandler1(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="forumAudit" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4">
                        <script>
                            function myOnClickHandler2(obj) {
                                var input = document.getElementById("ForumReplyAudit");
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">帖子评论审核</label>
                        <?php
                        if ($forumReplyAudit=='1') {
                            echo '<input type="checkbox" name="ForumReplyAudit" id="ForumReplyAudit" value="1" data-switch="success"
                               onclick="myOnClickHandler2(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="ForumReplyAudit" id="ForumReplyAudit" value="0" data-switch="success"
                               onclick="myOnClickHandler2(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="ForumReplyAudit" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group col-sm-4">
                        <script>
                            function myOnClickHandler3(obj) {
                                var input = document.getElementById("spaceAudit");
                                console.log(input);
                                if (obj.checked) {
                                    input.setAttribute("value", "1");
                                } else {
                                    input.setAttribute("value", "0");
                                }
                            }
                        </script>
                        <label for="validationCustom08">动态审核</label>
                        <?php
                        if ($spaceAudit=='1') {
                            echo '<input type="checkbox" name="spaceAudit" id="spaceAudit" value="1" data-switch="success"
                               onclick="myOnClickHandler3(this)" checked>';
                        }else{
                            echo '<input type="checkbox" name="spaceAudit" id="spaceAudit" value="0" data-switch="success"
                               onclick="myOnClickHandler3(this)">';
                        }
                        ?>
                        
                        <label id="switchurl" style="display:block;" for="spaceAudit" data-on-label="打开"
                               data-off-label="关闭"></label>
                    </div>
                    <div class="form-group mb-3 text_right">
                        <button class="btn btn-success" type="submit" id="setAuditPost">保存修改</button>
                    </div>
                </form>

            </div>  
        </div> 
    </div>  
</div>


<!--<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>-->




<?php
include_once 'Footer.php';
?>

</body>
</html>