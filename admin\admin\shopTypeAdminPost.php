<?php
session_start();
?>

<?php
include_once 'connect.php';
$id = $_GET['id'];
$status = $_GET['status'];
$file = $_SERVER['PHP_SELF'];

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    if ($status === 'Del') {
        $sql = "DELETE FROM typecho_shoptype WHERE id = '$id'";
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('删除成功');history.back();</script>";
        } else {
            echo "<script>alert('删除失败')';history.back();</script>";
        }
    }  else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}