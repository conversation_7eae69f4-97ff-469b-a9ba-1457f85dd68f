
editorConfig.MENU_CONF = {
    uploadImage: {  
        server: '<?php echo $API_UPLOAD_FULL ?>',
        fieldName: 'file',
        maxFileSize: 50 * 1024 * 1024,
        maxNumberOfFiles: 10,
        allowedFileTypes: ['image/*'],
        meta: {
             webkey: '<?php echo $api_key ?>'
        },
        metaWithUrl: false,
        onSuccess: function(file, res) {  
            // 单个文件上传成功之后的回调函数  
            toastr.success(`上传成功`, "提示"); 
        },  
        onFailed: function(file, res) {  
            // 单个文件上传失败的回调函数  
            toastr.error(`${res.message}`, "上传失败"); 
        },  
        onError: function(file, err, res) {  
            // 上传错误或者触发 timeout 超时的回调函数  
            toastr.error(`${err} | ${res.message}`, "上传失败"); 
        }  
        
    },
    uploadVideo: {  
        server: '<?php echo $API_UPLOAD_FULL ?>',
        fieldName: 'file',
        maxFileSize: 50 * 1024 * 1024,
        allowedFileTypes: ['video/*'],
        meta: {
             webkey: '<?php echo $api_key ?>'
        },
        metaWithUrl: false,
        onSuccess: function(file, res) {  
            // 单个文件上传成功之后的回调函数  
            toastr.success(`上传成功`, "提示"); 
        },  
        onFailed: function(file, res) {  
            // 单个文件上传失败的回调函数  
            toastr.error(`${res.message}`, "上传失败"); 
        },  
        onError: function(file, err, res) {  
            // 上传错误或者触发 timeout 超时的回调函数  
            toastr.error(`${err} | ${res.message}`, "上传失败"); 
        }  
    }
    
}
const editor = createEditor({
    selector: '#editor-container',
    <?php
    if ($edithOf == true) {
        echo "html: document.getElementById('editorContent').value,";
    }else{
        echo "html: '<p><br></p>',";
    }
    ?>
    config: editorConfig,
    mode: 'simple',
})
const toolbarConfig = {}


toolbarConfig.toolbarKeys = [
    "blockquote",
    "header1",
    "header2",
    "header3",
    "|",
    "bold",
    "underline",
    "italic",
    "through",
    "color",
    "bgColor",
    "clearStyle",
    "|",
    "bulletedList",
    "numberedList",
    "justifyLeft",
    "justifyRight",
    "justifyCenter" ,
    "|",
    "insertLink",
    {
        key: 'group-image', 
        title: '图片', 
        iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>', 
        menuKeys:  ['insertImage', 'uploadImage']
        
    },
    {
        key: 'group-video', 
        title: '视频', 
        iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z"></path></svg>', 
        menuKeys: ['insertVideo', 'uploadVideo']
        
    },
    "insertTable",
    "codeBlock",
    "|",
    "undo",
    "redo"
]
const toolbar = createToolbar({
    editor,
    selector: '#toolbar-container',
    config: toolbarConfig,
    mode: 'simple', // or 'simple'
})

</script>