<?php
session_start();
?>

<?php
$uid = $_POST['uid'];
$name = $_POST['name'];
$screenName = $_POST['screenName'];
$mail = $_POST['mail'];
$url = $_POST['url'];
$customize = $_POST['customize'];
$group = $_POST['group'];
$password = '$P$BjzPjbwyjKHLlq8JmRClxHFpmxtTxw1';
// 检查变量是否为空，如果为空，则将其设置为NULL
if (empty($screenName)) {
    $screenName = NULL;
}
if (empty($url)) {
    $url = NULL;
}
if (empty($customize)) {
    $customize = NULL;
}

$file = $_SERVER['PHP_SELF'];

include_once 'connect.php';
//
if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    $checkQuery = "SELECT * FROM typecho_users WHERE name = '$name' OR mail = '$mail'";
    $checkStmt = mysqli_query($connect, $checkQuery);
    
    if (mysqli_num_rows($checkStmt) > 0) {
        echo "<script>alert('用户名或邮箱已存在，请重新输入');history.back();</script>";
    } else {
        $updateQuery = "insert into typecho_users (`group`,password,name,screenName,mail,url,customize) values ('$group','$password','$name','$screenName','$mail','$url','$customize')";
       $updateStmt = mysqli_query($connect, $updateQuery);
        
        if (!$updateStmt) {
            echo "<script>alert('添加失败');location.href = 'userAdmin.php';</script>";
        } else {
            echo "<script>alert('添加成功');location.href = 'userAdmin.php';</script>";
        }
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}