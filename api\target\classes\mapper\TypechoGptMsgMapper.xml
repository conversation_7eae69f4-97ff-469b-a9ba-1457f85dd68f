<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoGptMsgDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoGptMsg" >
        <result column="id" property="id" />
        <result column="uid" property="uid" />
        <result column="gptid" property="gptid" />
        <result column="text" property="text" />
        <result column="created" property="created" />
        <result column="isAI" property="isAI" />

    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `uid`,
        `gptid`,
        `text`,
        `created`,
        `isAI`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoGptMsg">
        INSERT INTO ${prefix}_gpt_msg
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != gptid'>
                `gptid`,
            </if>
            <if test ='null != text'>
                `text`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != isAI'>
                `isAI`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != gptid'>
                #{gptid},
            </if>
            <if test ='null != text'>
                #{text},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != isAI'>
                #{isAI}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_gpt_msg ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.uid},
                #{curr.gptid},
                #{curr.text},
                #{curr.created}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoGptMsg">
        UPDATE ${prefix}_gpt_msg
        <set>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != gptid'>`gptid` = #{gptid},</if>
            <if test ='null != text'>`text` = #{text},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != isAI'>`isAI` = #{isAI}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_gpt_msg
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_gpt_msg WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_gpt_msg
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_gpt_msg
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != gptid'>
                and `gptid` = #{gptid}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != isAI'>
                and `isAI` = #{isAI}
            </if>
        </where>
        order by created desc
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_gpt_msg
        <where>
            <if test ='null != typechoGptMsg.id'>
                and `id` = #{typechoGptMsg.id}
            </if>
            <if test ='null != typechoGptMsg.uid'>
                and `uid` = #{typechoGptMsg.uid}
            </if>
            <if test ='null != typechoGptMsg.gptid'>
                and `gptid` = #{typechoGptMsg.gptid}
            </if>
            <if test ='null != typechoGptMsg.text'>
                and `text` = #{typechoGptMsg.text}
            </if>
            <if test ='null != typechoGptMsg.created'>
                and `created` = #{typechoGptMsg.created}
            </if>
            <if test ='null != typechoGptMsg.isAI'>
                and `isAI` = #{typechoGptMsg.isAI}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`text`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
        order by created desc
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_gpt_msg
        <where>
            <if test ='null != typechoGptMsg.id'>
                and `id` = #{typechoGptMsg.id}
            </if>
            <if test ='null != typechoGptMsg.uid'>
                and `uid` = #{typechoGptMsg.uid}
            </if>
            <if test ='null != typechoGptMsg.gptid'>
                and `gptid` = #{typechoGptMsg.gptid}
            </if>
            <if test ='null != typechoGptMsg.text'>
                and `text` = #{typechoGptMsg.text}
            </if>
            <if test ='null != typechoGptMsg.created'>
                and `created` = #{typechoGptMsg.created}
            </if>
            <if test ='null != typechoGptMsg.isAI'>
                and `isAI` = #{typechoGptMsg.isAI}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`text`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
    </select>
</mapper>