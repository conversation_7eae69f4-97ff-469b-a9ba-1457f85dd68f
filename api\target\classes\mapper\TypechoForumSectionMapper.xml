<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoForumSectionDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoForumSection" >
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="pic" property="pic" />
        <result column="bg" property="bg" />
        <result column="text" property="text" />
        <result column="type" property="type" />
        <result column="restrict" property="restrictKey" />
        <result column="parent" property="parent" />
        <result column="slug" property="slug" />
        <result column="order" property="orderKey" />
        <result column="isrecommend" property="isrecommend" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `name`,
        `pic`,
        `bg`,
        `text`,
        `type`,
        `restrict`,
        `parent`,
        `slug`,
        `order`,
        `isrecommend`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoForumSection">
        INSERT INTO ${prefix}_forum_section
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != name'>
                `name`,
            </if>
            <if test ='null != pic'>
                `pic`,
            </if>
            <if test ='null != bg'>
                `bg`,
            </if>
            <if test ='null != text'>
                `text`,
            </if>
            <if test ='null != type'>
                `type`,
            </if>
            <if test ='null != restrictKey'>
                `restrict`,
            </if>
            <if test ='null != parent'>
                `parent`,
            </if>
            <if test ='null != slug'>
                `slug`,
            </if>
            <if test ='null != orderKey'>
                `order`,
            </if>
            <if test ='null != isrecommend'>
                `isrecommend`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != name'>
                #{name},
            </if>
            <if test ='null != pic'>
                #{pic},
            </if>
            <if test ='null != bg'>
                #{bg},
            </if>
            <if test ='null != text'>
                #{text},
            </if>
            <if test ='null != type'>
                #{type},
            </if>
            <if test ='null != restrictKey'>
                #{restrictKey},
            </if>
            <if test ='null != parent'>
                #{parent},
            </if>
            <if test ='null != slug'>
                #{slug},
            </if>
            <if test ='null != orderKey'>
                #{orderKey}
            </if>,
            <if test ='null != isrecommend'>
                #{isrecommend}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_forum_section ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.name},
                #{curr.pic},
                #{curr.text},
                #{curr.type},
                #{curr.restrictKey}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoForumSection">
        UPDATE ${prefix}_forum_section
        <set>
            <if test ='null != name'>`name` = #{name},</if>
            <if test ='null != pic'>`pic` = #{pic},</if>
            <if test ='null != bg'>`bg` = #{bg},</if>
            <if test ='null != text'>`text` = #{text},</if>
            <if test ='null != type'>`type` = #{type},</if>
            <if test ='null != restrictKey'>`restrict` = #{restrictKey},</if>
            <if test ='null != parent'>`parent` = #{parent},</if>
            <if test ='null != slug'>`slug` = #{slug},</if>
            <if test ='null != orderKey'>`order` = #{orderKey},</if>
            <if test ='null != isrecommend'>`isrecommend` = #{isrecommend}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_forum_section
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_forum_section WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_section
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_section
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != name'>
                and `name` = #{name}
            </if>
            <if test ='null != pic'>
                and `pic` = #{pic}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
            <if test ='null != restrictKey'>
                and `restrict` = #{restrictKey}
            </if>
            <if test ='null != parent'>
                and `parent` = #{parent}
            </if>
            <if test ='null != isrecommend'>
                and `isrecommend` = #{isrecommend}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_section
        <where>
            <if test ='null != typechoForumSection.id'>
                and `id` = #{typechoForumSection.id}
            </if>
            <if test ='null != typechoForumSection.name'>
                and `name` = #{typechoForumSection.name}
            </if>
            <if test ='null != typechoForumSection.pic'>
                and `pic` = #{typechoForumSection.pic}
            </if>
            <if test ='null != typechoForumSection.text'>
                and `text` = #{typechoForumSection.text}
            </if>
            <if test ='null != typechoForumSection.type'>
                and `type` = #{typechoForumSection.type}
            </if>
            <if test ='null != typechoForumSection.restrictKey'>
                and `restrict` = #{typechoForumSection.restrictKey}
            </if>
            <if test ='null != typechoForumSection.parent'>
                and `parent` = #{typechoForumSection.parent}
            </if>
            <if test ='null != typechoForumSection.isrecommend'>
                and `isrecommend` = #{typechoForumSection.isrecommend}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`name`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
        <if test="order != null and order != ''">
            order by `${order}` desc
        </if>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_forum_section
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != name'>
                and `name` = #{name}
            </if>
            <if test ='null != pic'>
                and `pic` = #{pic}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
            <if test ='null != restrictKey'>
                and `restrict` = #{restrictKey}
            </if>
            <if test ='null != isrecommend'>
                and `isrecommend` = #{isrecommend}
            </if>
        </where>
    </select>
</mapper>