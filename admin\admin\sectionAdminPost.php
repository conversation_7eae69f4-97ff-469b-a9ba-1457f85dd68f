<?php
session_start();
?>

<?php
include_once 'connect.php';
$id = $_GET['id'];
$status = $_GET['status'];
$file = $_SERVER['PHP_SELF'];

if (isset($_SESSION['loginadmin']) && $_SESSION['loginadmin'] <> '') {
$redisKeys = $connectRedis->keys('starapi_*');
    foreach ($redisKeys as $redisKey) {
        $connectRedis->del($redisKey);
    }
    
    if ($status === 'Tj') {
        $sql = "SELECT `type` FROM `typecho_forum_section` WHERE `id` = ?";  
        $stmt = mysqli_prepare($connect, $sql);  
        mysqli_stmt_bind_param($stmt, "i", $id); // 假设 id 是整数  
        mysqli_stmt_execute($stmt);  
        mysqli_stmt_store_result($stmt);  
        mysqli_stmt_bind_result($stmt, $type);  
          
        while (mysqli_stmt_fetch($stmt)) {  
            if ($type == 'sort') {  
                echo "<script>alert('不能推荐大类');history.back();</script>";  
                exit();  
            }  
        } 
        mysqli_stmt_close($stmt);
        $sql = "UPDATE typecho_forum_section SET isrecommend = '1' WHERE id = ?";  
        $stmt = mysqli_prepare($connect, $sql);  
        mysqli_stmt_bind_param($stmt, "i", $id); // 假设 id 是整数  
        $result = mysqli_stmt_execute($stmt);  
          
        if ($result) {  
            echo "<script>alert('推荐成功');history.back();</script>";  
        } else {  
            echo "<script>alert('推荐失败');history.back();</script>";  
        }  
        mysqli_stmt_close($stmt);
    } else if($status === 'Qx'){
       $sql = "SELECT `type` FROM `typecho_forum_section` WHERE `id` = ?";  
        $stmt = mysqli_prepare($connect, $sql);  
        mysqli_stmt_bind_param($stmt, "i", $id); // 假设 id 是整数  
        mysqli_stmt_execute($stmt);  
        mysqli_stmt_store_result($stmt);  
        mysqli_stmt_bind_result($stmt, $type);  
          
        while (mysqli_stmt_fetch($stmt)) {  
            if ($type == 'sort') {  
                echo "<script>alert('不能推荐大类');history.back();</script>";  
                exit();  
            }  
        }  
        mysqli_stmt_close($stmt);
        $sql = "UPDATE typecho_forum_section SET isrecommend = '0' WHERE id = ?";  
        $stmt = mysqli_prepare($connect, $sql);  
        mysqli_stmt_bind_param($stmt, "i", $id); // 假设 id 是整数  
        $result = mysqli_stmt_execute($stmt);  
        if ($result) {  
            echo "<script>alert('推荐成功');history.back();</script>";  
        } else {  
            echo "<script>alert('推荐失败');history.back();</script>";  
        }  
        mysqli_stmt_close($stmt);
    } else if ($status === 'Del') {
        $sql = "DELETE FROM typecho_forum_section WHERE id = '$id'";
        $result = mysqli_query($connect, $sql);
        if ($result) {
            echo "<script>alert('删除成功');history.back();</script>";
        } else {
            echo "<script>alert('删除失败')';history.back();</script>";
        }
    }  else {
        echo "<script>alert('参数错误');history.back();</script>";
    }
} else {
    echo "<script>alert('非法操作，行为已记录');location.href = 'warning.php?route=$file';</script>";
}